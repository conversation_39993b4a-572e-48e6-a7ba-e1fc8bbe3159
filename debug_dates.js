const fs = require('fs');
const path = require('path');

// Debug script to check date generation and front month resolution
const CONFIG = {
  ohlcvDir: 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/ohlcv',
};

function quarterMonths() { return [3, 6, 9, 12]; } // Mar, Jun, Sep, Dec

function thirdFriday(year, month) {
  const d = new Date(Date.UTC(year, month - 1, 1));
  while (d.getUTCDay() !== 5) d.setUTCDate(d.getUTCDate() + 1);
  d.setUTCDate(d.getUTCDate() + 14);
  return d;
}

function mondayPrior(d) {
  d = new Date(d);
  while (d.getUTCDay() !== 1) d.setUTCDate(d.getUTCDate() - 1);
  return d;
}

function resolveFrontMonthSymbol(date) {
  const y0 = date.getUTCFullYear() - 1;
  const candidates = [];
  for (let y = y0; y <= y0 + 3; y++) {
    for (const m of quarterMonths()) {
      const tf = thirdFriday(y, m); const roll = mondayPrior(tf);
      candidates.push({ year: y, month: m, rollover: roll });
    }
  }
  candidates.sort((a, b) => a.rollover.getTime() - b.rollover.getTime());
  
  for (const c of candidates) {
    if (c.rollover > date) {
      const monthCode = { 3: 'H', 6: 'M', 9: 'U', 12: 'Z' }[c.month];
      const yearCode = c.year.toString().slice(-1);
      return `MNQ${monthCode}${yearCode}`;
    }
  }
  return null;
}

function generateDateRange() {
  const ohlcvFiles = fs.readdirSync(CONFIG.ohlcvDir).filter(f => f.endsWith('.csv'));
  const dates = new Set();
  
  for (const file of ohlcvFiles) {
    const match = file.match(/(\d{8})/);
    if (match) dates.add(match[1]);
  }
  
  return Array.from(dates).sort();
}

async function debugDates() {
  console.log('🔍 DEBUGGING DATE GENERATION AND FRONT MONTH RESOLUTION...\n');
  
  // Generate dates from available files
  const dates = generateDateRange();
  console.log(`📅 Found ${dates.length} dates from OHLCV files`);
  console.log('First 10 dates:', dates.slice(0, 10));
  console.log('Last 10 dates:', dates.slice(-10));
  
  // Test front month resolution for sample dates
  console.log('\n🔄 Testing front month resolution:');
  const sampleDates = dates.filter((_, i) => i % 20 === 0); // Every 20th date
  
  for (const dateStr of sampleDates) {
    const y = Number(dateStr.slice(0,4));
    const m = Number(dateStr.slice(4,6))-1;
    const d = Number(dateStr.slice(6,8));
    const date = new Date(Date.UTC(y, m, d));
    const symbol = resolveFrontMonthSymbol(date);
    console.log(`  ${dateStr} -> ${symbol}`);
  }
  
  // Check what files exist for each symbol
  console.log('\n📁 Checking available OHLCV files by symbol:');
  const symbolCounts = {};
  const ohlcvFiles = fs.readdirSync(CONFIG.ohlcvDir).filter(f => f.endsWith('.csv'));
  
  for (const file of ohlcvFiles) {
    const symbolMatch = file.match(/\.(MNQ[HMUZ]\d)\.csv$/);
    if (symbolMatch) {
      const symbol = symbolMatch[1];
      symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    }
  }
  
  console.log('Symbol file counts:', symbolCounts);
  
  // Test specific date resolution
  console.log('\n🎯 Testing specific recent dates:');
  const recentDates = ['20240815', '20240816', '20250101', '20250102'];
  
  for (const dateStr of recentDates) {
    if (dates.includes(dateStr)) {
      const y = Number(dateStr.slice(0,4));
      const m = Number(dateStr.slice(4,6))-1;
      const d = Number(dateStr.slice(6,8));
      const date = new Date(Date.UTC(y, m, d));
      const symbol = resolveFrontMonthSymbol(date);
      
      // Check if file exists for this symbol and date
      const expectedFile = `glbx-mdp3-${dateStr}.ohlcv-1m.${symbol}.csv`;
      const fileExists = fs.existsSync(path.join(CONFIG.ohlcvDir, expectedFile));
      
      console.log(`  ${dateStr} -> ${symbol} (file exists: ${fileExists})`);
      if (!fileExists) {
        console.log(`    Expected: ${expectedFile}`);
        // List actual files for this date
        const actualFiles = ohlcvFiles.filter(f => f.includes(dateStr));
        console.log(`    Actual files: ${actualFiles.join(', ')}`);
      }
    } else {
      console.log(`  ${dateStr} -> NOT IN GENERATED DATES`);
    }
  }
}

debugDates().catch(console.error);
