// ========================
// backtest.js
// V10.40: Advanced ML Filter, Time Analysis, and Dynamic Position Sizing
// ========================

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
const config = require('./config');
const spreadUtils = require('./spread_volatility_utils');
const enhancedPM = require('./enhanced_position_management');
// Optional tick replay module (guarded require)
const tickReplay = (() => { try { return require('./tick_replay'); } catch (e) { return null; } })();

// Import and initialize time analysis
const AdvancedTimeAnalysis = config.useTimeAnalysis ? require('./advanced_time_analysis') : null;
const timeAnalyzer = config.useTimeAnalysis ? new AdvancedTimeAnalysis(config) : null;

// Initialize ML filter and position sizer (set to null if not used)
const mlFilter = null; // ML filter not implemented yet
const positionSizer = null; // Position sizer not implemented yet

// --- Pattern Detection & Entry Filters (moved to top for availability) ---
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : 'red';
}

function detect3(c1, c2, c3) {
    // Basic validation
    if (!c1 || !c2 || !c3) return null;

    // Get candle colors
    const col1 = candlestickColor(c1),
          col2 = candlestickColor(c2),
          col3 = candlestickColor(c3);

    // Validate candle colors
    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: green-red-green (SIMPLE - just color pattern)
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }

    // Bearish pattern: red-green-red (SIMPLE - just color pattern)
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }

    return null;
}

// Dynamic take profit points based on time of day (matching live trading)
// Global variable to hold current TP configuration for testing
let CURRENT_TP_CONFIG = null;

const DYNAMIC_TP_POINTS = {
    MNQ: {
        // CST hours (trading hours 8:15 AM - 3:50 PM CST)
        8: 15,   // 8:00-8:59 AM CST - Market open, high volatility
        9: 15,   // 9:00-9:59 AM CST - High volatility continues
        10: 10,  // 10:00-10:59 AM CST - Moderate activity
        11: 15,  // 11:00-11:59 AM CST - Active period
        12: 15,  // 12:00-12:59 PM CST - Active period continues
        13: 10,  // 1:00-1:59 PM CST - Afternoon, slower
        14: 10,  // 2:00-2:59 PM CST - Late afternoon
        15: 10   // 3:00-3:59 PM CST - End of day
    },
    MES: {
        8: 7, 9: 7, 10: 5, 11: 7, 12: 7, 13: 5, 14: 5, 15: 5
    },
    MGC: {
        8: 4, 9: 4, 10: 3, 11: 4, 12: 4, 13: 3, 14: 3, 15: 3
    },
    M2K: {
        8: 7, 9: 7, 10: 5, 11: 7, 12: 7, 13: 5, 14: 5, 15: 5
    }
};

/**
 * Get dynamic take profit points based on timestamp
 * @param {number} timestamp - Unix timestamp
 * @param {string} symbol - Trading symbol (e.g., 'MNQ')
 * @returns {number} - Take profit points for the hour
 */
function getDynamicTpPoints(timestamp, symbol = 'MNQ') {
    const baseSymbol = symbol.substring(0, 3);
    const date = new Date(timestamp * 1000);

    // Convert UTC to CST (UTC - 6 hours)
    const cstHour = (date.getUTCHours() - 6 + 24) % 24;

    // Use current TP config if testing, otherwise use default
    const tpSource = CURRENT_TP_CONFIG || DYNAMIC_TP_POINTS[baseSymbol];
    if (tpSource && tpSource[cstHour] !== undefined) {
        return tpSource[cstHour];
    }

    // Fallback to default
    return 10;
}

// 4-candle pattern detection removed - using only 3-candle patterns for simplicity

/**
 * Generate TP optimization configurations
 * Tests TP values from 6 to 20 for each trading hour
 */
function generateTPOptimizationConfigs() {
    const configs = [];
    const tpRange = [6, 8, 10, 12, 15, 18, 20]; // Test these TP values
    const tradingHours = [8, 9, 10, 11, 12, 13, 14, 15]; // CST hours

    // Test uniform TP across all hours first
    for (const tp of tpRange) {
        const config = {};
        for (const hour of tradingHours) {
            config[hour] = tp;
        }
        configs.push({
            name: `Uniform_TP_${tp}`,
            config: config
        });
    }

    return configs;
}

// Global debug counters
let debugCounters = {
    totalChecks: 0,
    patternFound: 0,
    timeFilter: 0,
    wmaFilter: 0,
    wma200Filter: 0,
    rsiFilter: 0,
    macdFilter: 0,
    finalPass: 0
};

function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod, params = null) {
    debugCounters.totalChecks++;

    // Basic validation - exclude RSI if disabled
    const rsiEnabled = config.useRSIFilter !== false && (config.strategy?.rsiPeriod || config.rsiPeriod) > 0 && (config.strategy?.rsiMaPeriod || config.rsiMaPeriod) > 0;
    if (isNaN(c3.wma50)) {
        return false;
    }
    if (rsiEnabled && (isNaN(c3.rsi) || isNaN(c3.rsiMa))) {
        return false;
    }

    debugCounters.patternFound++;

    // Debug: Track filtering reasons
    let debugReason = null;

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }
    debugCounters.wmaFilter++;

    // USER'S EXACT EDGE: Price above/below BOTH MAs for trend direction
    if (config.use200WmaFilter) {
        // For LONG trades: price must be above BOTH 50 WMA AND 200 WMA
        if (dir === 'bullish') {
            if (c3.close <= c3.wma50 || c3.close <= c3.wma200) {
                return false;
            }
        }
        // For SHORT trades: price must be below BOTH 50 WMA AND 200 WMA
        if (dir === 'bearish') {
            if (c3.close >= c3.wma50 || c3.close >= c3.wma200) {
                return false;
            }
        }
    }
    debugCounters.wma200Filter++;

    // RSI filter for all patterns (only if RSI is enabled)
    if (rsiEnabled) {
        // UPDATED RSI level conditions (using config values)
        // Longs only if RSI > rsiUpperBand, Shorts only if RSI < rsiLowerBand
        const rsiUpperThreshold = config.rsiUpperBand || 60;
        const rsiLowerThreshold = config.rsiLowerBand || 40;

        // Debug: Log RSI values for first few patterns
        if (debugCounters.totalChecks <= 5) {
            console.log(`DEBUG RSI: ${dir} pattern, RSI=${c3.rsi?.toFixed(1)}, RSI-MA=${c3.rsiMa?.toFixed(1)}, Upper=${rsiUpperThreshold}, Lower=${rsiLowerThreshold}`);
        }

        if (dir === 'bullish' && c3.rsi <= rsiUpperThreshold) {
            debugReason = `RSI_LEVEL: ${dir} RSI=${c3.rsi?.toFixed(1)} <= ${rsiUpperThreshold}`;
            return false;
        }
        if (dir === 'bearish' && c3.rsi >= rsiLowerThreshold) {
            debugReason = `RSI_LEVEL: ${dir} RSI=${c3.rsi?.toFixed(1)} >= ${rsiLowerThreshold}`;
            return false;
        }
    }

    // RSI-MA directional filter (if enabled)
    if (rsiEnabled && config.useRsiMaFilter) {
        // User requested: do NOT use RSI-MA filter; accept RSI-only
        // Keeping branch for compatibility, but always pass through
    }

    // ATR filter - only trade during optimal volatility conditions (use params if available)
    const minAtr = (typeof params !== 'undefined' && params.minAtrEntry !== undefined) ? params.minAtrEntry : config.minAtrEntry;
    const maxAtr = (typeof params !== 'undefined' && params.maxAtrEntry !== undefined) ? params.maxAtrEntry : config.maxAtrEntry;

    if (config.useATRFilter && minAtr > 0) {
        if (!c3.atr || c3.atr < minAtr) {
            return false;
        }
    }
    if (config.useATRFilter && maxAtr && maxAtr < 999) {
        if (!c3.atr || c3.atr > maxAtr) {
            return false;
        }
    }

    // RSI-MA separation filter disabled per request (RSI-only)

    // ML-based entry filter
    if (config.useMLEntryFilter && mlFilter) {
        // Get the timestamp
        const timestamp = new Date(c3.timestamp * 1000);

        // Get the hour of day (UTC)
        const hourOfDay = timestamp.getUTCHours();

        // Get the day of week (0 = Sunday, 6 = Saturday)
        const dayOfWeek = timestamp.getUTCDay();

        // Determine ATR regime
        const atrRegime = c3.atr < config.atrThresholds?.low_medium ? 'Low' :
                         (c3.atr > config.atrThresholds?.medium_high ? 'High' : 'Medium');

        // Get recent candles for pattern analysis if available
        let recentCandles = [];
        if (candlesForPeriod && Array.isArray(candlesForPeriod) && currentIndex !== undefined) {
            const lookbackStart = Math.max(0, currentIndex - 5);
            recentCandles = candlesForPeriod.slice(lookbackStart, currentIndex + 1);
        }

        // Create feature object for ML filter
        const features = {
            direction: dir,
            atrRegime: atrRegime,
            hourOfDay: hourOfDay,
            dayOfWeek: dayOfWeek,
            rsi: c3.rsi,
            rsiMa: c3.rsiMa,
            close: c3.close,
            wma50: c3.wma50,
            patternType: patternType,
            recentCandles: recentCandles,
            timestamp: timestamp
        };

        // Get time score if time analysis is enabled
        let timeScore = 0.5; // Default neutral score
        if (config.useTimeAnalysis && timeAnalyzer) {
            const timeScoreInfo = timeAnalyzer.getTimeScore(timestamp);
            timeScore = timeScoreInfo.combinedScore;

            // Add time score to features
            features.timeScore = timeScore;
        }

        // Use ML filter to decide whether to take the entry
        return mlFilter.shouldEnter(features);
    }

    // MACD confluence filter (NEW)
    if (config.useMACDFilter) {
        if (isNaN(c3.macd)) {
            return false; // No MACD data available
        }

        // For BULLISH trades: MACD must be positive (green)
        if (dir === 'bullish' && c3.macd <= 0) {
            return false;
        }

        // For BEARISH trades: MACD must be negative (red)
        if (dir === 'bearish' && c3.macd >= 0) {
            return false;
        }
    }
    debugCounters.macdFilter++;

    // All hit rate improvement filters removed - back to baseline

    debugCounters.rsiFilter++;
    debugCounters.finalPass++;
    return true;
}

// --- VERY EARLY LOG ---
console.log("Script started. Attempting to require config...");
// --- END EARLY LOG ---


// --- *** Define Output Directory *** ---
const outputDir = 'C:/backtest-bot/output/MNQ_LastYear_TradeLog'; // << SET FOR TRADE LOG GENERATION
// Additional deliverables directory on D: as requested
const deliverablesDir = 'D:/backtest-output/aug-grid-deliverables';
try { fs.mkdirSync(deliverablesDir, { recursive: true }); } catch (e) { console.warn('Could not ensure deliverablesDir:', e?.message); }
// --- *********************************** ---

// --- 1) Setup & Initialization ---
if (!fs.existsSync(outputDir)) { try { fs.mkdirSync(outputDir, { recursive: true }); console.log(`Created output directory: ${outputDir}`); } catch (err) { console.error(`Error creating output directory '${outputDir}':`, err); process.exit(1); } } else { console.log(`Output directory exists: ${outputDir}`); }
let allCandles = []; let allRunResults = [];
const atrThresholds = { low_medium: 4.7601, medium_high: 7.2605 }; // Might need adjustment for MYM ATR values later
const adaptiveParams = {
    Low: { slFactor: 3.0, tpFactor: 5.0, trailFactor: 0.11 },     // OPTIMIZED: SL=3, TP=5 from validation
    Medium: { slFactor: 3.0, tpFactor: 5.0, trailFactor: 0.11 },  // OPTIMIZED: SL=3, TP=5 from validation
    High: { slFactor: 3.0, tpFactor: 5.0, trailFactor: 0.11 }     // OPTIMIZED: SL=3, TP=5 from validation
};

// --- Data Loading & Parsing ---
if (!config) { console.error("FATAL: Config object is undefined after require."); process.exit(1);}
if (!config.inputFile || typeof config.inputFile !== 'string') { console.error("Error: 'inputFile' not defined or invalid in config.js"); process.exit(1); }
if (!fs.existsSync(config.inputFile)) { console.error(`Error: Input file not found at path: ${config.inputFile}`); process.exit(1); }

console.log(`Attempting to read: ${config.inputFile}`);

// First check the file format by reading the header
const headerLine = fs.readFileSync(config.inputFile, 'utf8').split('\n')[0];
console.log(`CSV Header: ${headerLine}`);

// Determine if this is a Databento format file
const isDatabentoFormat = headerLine.includes('ts_event');
const separator = headerLine.includes(';') ? ';' : ',';

console.log(`Detected format: ${isDatabentoFormat ? 'Databento' : 'Standard'}, separator: '${separator}'`);

fs.createReadStream(config.inputFile)
    .pipe(csv({
        separator: separator,
        mapHeaders: ({ header }) => header.trim()
    }))
    .on('data', d => {
        let open, high, low, close, timestampSeconds;

        if (isDatabentoFormat) {
            // Databento format
            open = parseFloat(d['open']);
            high = parseFloat(d['high']);
            low = parseFloat(d['low']);
            close = parseFloat(d['close']);

            // Parse timestamp from ts_event field (Databento uses nanoseconds)
            const tsEvent = d['ts_event'];
            if (tsEvent) {
                const tsNum = Number(tsEvent);
                // Convert nanoseconds to seconds: divide by 1e9
                if (tsNum > 1e15) {
                    timestampSeconds = Math.floor(tsNum / 1000000000); // nanoseconds to seconds
                } else if (tsNum > 1e11) {
                    timestampSeconds = Math.floor(tsNum / 1000); // milliseconds to seconds
                } else {
                    timestampSeconds = tsNum; // already in seconds
                }
            }

            // Filter by symbol - AUTO-DETECT CONTRACT MONTHS
            const symbol = d['symbol'];
            if (symbol && (!symbol.startsWith('MNQ') || symbol.includes('-'))) {
                return; // Skip non-MNQ symbols and spread data
            }

            // Store symbol for contract splitting
            if (!global.detectedContracts) {
                global.detectedContracts = new Set();
            }
            if (symbol && symbol.startsWith('MNQ')) {
                global.detectedContracts.add(symbol);
            }

            // Additional safety check: reject any prices that look like spread data
            // Lowered threshold to accommodate historical MNQ prices
            if (open < 50 || high < 50 || low < 50 || close < 50) {
                console.log(`🚨 REJECTED SPREAD-LIKE DATA: ${symbol} - O=${open}, H=${high}, L=${low}, C=${close}`);
                return; // Skip data that looks like spread prices
            }
        } else {
            // Standard format
            open = +d['Open'];
            high = +d['High'];
            low = +d['Low'];
            close = +d['Close'];
            const timeString = d['Time'] || d['Date'] || d['Time left'];

            if (timeString) {
                let parsedDate;
                try { parsedDate = new Date(timeString); } catch (e) {}
                if (parsedDate && !isNaN(parsedDate.getTime())) {
                    timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                } else if (timeString && !isNaN(Number(timeString))) {
                    const tsNum = Number(timeString);
                    timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                }
            }
        }

        if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            // Skip invalid data
            return;
        }

        allCandles.push({ timestamp: timestampSeconds, open, high, low, close, symbol: d['symbol'] || 'MNQ' });
    })
    .on('error', (error) => {
        console.error('CSV parsing error:', error);
        process.exit(1);
    })
    .on('end', () => {
        console.log("CSV parsing finished ('end' event received).");
        if (allCandles.length === 0) {
            console.error(`Error: No valid candle data parsed from ${config.inputFile}. Check headers and format.`);
            process.exit(1);
        }

        console.log(`Parsed ${allCandles.length} initial candles...`);
        allCandles.sort((a, b) => a.timestamp - b.timestamp);
        console.log(`Data time range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);

        // Apply front month date filtering if specified
        if (config.frontMonthStart && config.frontMonthEnd) {
            const startDate = new Date(config.frontMonthStart + 'T00:00:00Z');
            const endDate = new Date(config.frontMonthEnd + 'T23:59:59Z');
            const startTimestamp = Math.floor(startDate.getTime() / 1000);
            const endTimestamp = Math.floor(endDate.getTime() / 1000);

            const originalLength = allCandles.length;
            allCandles = allCandles.filter(candle =>
                candle.timestamp >= startTimestamp && candle.timestamp <= endTimestamp
            );

            console.log(`Front month filtering applied: ${config.frontMonthStart} to ${config.frontMonthEnd}`);
            console.log(`Filtered from ${originalLength} to ${allCandles.length} candles`);

            if (allCandles.length === 0) {
                console.error(`Error: No candles remain after front month filtering. Check date range.`);
                process.exit(1);
            }

            console.log(`Filtered data time range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);
        }

        // Apply rollover cutoffs for single contract files
        if (config.useRolloverCutoffs && !config.isContractSplittingRun && allCandles.length > 0) {
            // Define contract active periods (start and end dates for each quarterly contract)
            const contractPeriods = {
                'MNQH4': { start: new Date('2023-12-15T00:00:00Z'), end: new Date('2024-03-01T23:59:59Z') }, // March 2024: Dec 2023 - Mar 2024
                'MNQM4': { start: new Date('2024-03-01T00:00:00Z'), end: new Date('2024-06-01T23:59:59Z') }, // June 2024: Mar 2024 - Jun 2024
                'MNQU4': { start: new Date('2024-06-01T00:00:00Z'), end: new Date('2024-09-01T23:59:59Z') }, // September 2024: Jun 2024 - Sep 2024
                'MNQZ4': { start: new Date('2024-09-01T00:00:00Z'), end: new Date('2024-12-01T23:59:59Z') }, // December 2024: Sep 2024 - Dec 2024
                'MNQH5': { start: new Date('2024-12-01T00:00:00Z'), end: new Date('2025-03-01T23:59:59Z') }, // March 2025: Dec 2024 - Mar 2025
                'MNQM5': { start: new Date('2025-03-01T00:00:00Z'), end: new Date('2025-06-01T23:59:59Z') }, // June 2025: Mar 2025 - Jun 2025
                'MNQU5': { start: new Date('2025-06-01T00:00:00Z'), end: new Date('2025-09-01T23:59:59Z') }, // September 2025: Jun 2025 - Sep 2025
                'MNQZ5': { start: new Date('2025-09-01T00:00:00Z'), end: new Date('2025-12-01T23:59:59Z') }, // December 2025: Sep 2025 - Dec 2025
                'MNQH6': { start: new Date('2025-12-01T00:00:00Z'), end: new Date('2026-03-01T23:59:59Z') }, // March 2026: Dec 2025 - Mar 2026
                'MNQM6': { start: new Date('2026-03-01T00:00:00Z'), end: new Date('2026-06-01T23:59:59Z') }, // June 2026: Mar 2026 - Jun 2026
                'MNQU6': { start: new Date('2026-06-01T00:00:00Z'), end: new Date('2026-09-01T23:59:59Z') }, // September 2026: Jun 2026 - Sep 2026
            };

            // Detect contract from first candle symbol
            const firstSymbol = allCandles[0].symbol;
            if (firstSymbol && contractPeriods[firstSymbol]) {
                const period = contractPeriods[firstSymbol];
                const startTimestamp = Math.floor(period.start.getTime() / 1000);
                const endTimestamp = Math.floor(period.end.getTime() / 1000);
                const originalCount = allCandles.length;

                // Filter to contract's active trading period
                allCandles = allCandles.filter(candle =>
                    candle.timestamp >= startTimestamp && candle.timestamp <= endTimestamp
                );

                console.log(`Applied contract period filter for ${firstSymbol}: ${originalCount} -> ${allCandles.length} candles`);
                console.log(`Active period: ${period.start.toISOString()} to ${period.end.toISOString()}`);
                if (allCandles.length > 0) {
                    console.log(`Actual data range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);
                }
            } else {
                console.log(`No contract period defined for symbol: ${firstSymbol}`);
            }
        }

        // Check if contract splitting is enabled
        if (config.isContractSplittingRun && global.detectedContracts && global.detectedContracts.size > 1) {
            console.log(`\n=== CONTRACT SPLITTING MODE ===`);
            console.log(`Detected ${global.detectedContracts.size} contracts: ${Array.from(global.detectedContracts).join(', ')}`);

            // Define rollover cutoff dates (end of peak trading period)
            const rolloverCutoffs = {
                'MNQH25': new Date('2025-02-28T23:59:59Z'), // March contract - stop before March rollover
                'MNQM25': new Date('2025-05-31T23:59:59Z'), // June contract - stop before June rollover
                'MNQU25': new Date('2025-08-31T23:59:59Z'), // September contract - stop before September rollover
                'MNQZ25': new Date('2025-11-30T23:59:59Z'), // December contract - stop before December rollover
                // Add 2024 contracts for historical data
                'MNQH24': new Date('2024-02-29T23:59:59Z'),
                'MNQM24': new Date('2024-05-31T23:59:59Z'),
                'MNQU24': new Date('2024-08-31T23:59:59Z'),
                'MNQZ24': new Date('2024-11-30T23:59:59Z'),
                // Add 2026 contracts for future data
                'MNQH26': new Date('2026-02-28T23:59:59Z'),
                'MNQM26': new Date('2026-05-31T23:59:59Z'),
                'MNQU26': new Date('2026-08-31T23:59:59Z'),
                'MNQZ26': new Date('2026-11-30T23:59:59Z')
            };

            // Run backtest for each contract separately
            for (const contract of Array.from(global.detectedContracts).sort()) {
                console.log(`\n--- Testing Contract: ${contract} ---`);

                // Filter data for this contract only
                let contractCandles = allCandles.filter(candle => candle.symbol === contract);

                // Apply rollover cutoff if enabled and cutoff date exists
                if (config.useRolloverCutoffs && rolloverCutoffs[contract]) {
                    const cutoffTimestamp = Math.floor(rolloverCutoffs[contract].getTime() / 1000);
                    const originalCount = contractCandles.length;
                    contractCandles = contractCandles.filter(candle => candle.timestamp <= cutoffTimestamp);
                    console.log(`Applied rollover cutoff for ${contract}: ${originalCount} -> ${contractCandles.length} candles (cutoff: ${rolloverCutoffs[contract].toISOString()})`);
                }

                console.log(`Contract ${contract}: ${contractCandles.length} candles`);

                if (contractCandles.length > 100) { // Only test contracts with sufficient data
                    console.log(`Data range: ${new Date(contractCandles[0].timestamp * 1000).toISOString()} to ${new Date(contractCandles[contractCandles.length - 1].timestamp * 1000).toISOString()}`);

                    console.log(`Calling runWeeklyBacktests for ${contract}...`);
                    // Create a temporary global variable for this contract's data
                    global.currentContractCandles = contractCandles;
                    global.currentContractName = contract;
                    runWeeklyBacktests();

                    console.log(`Completed backtest for ${contract}`);
                } else {
                    console.log(`Skipping ${contract} - insufficient data (${contractCandles.length} candles)`);
                }
            }

            console.log(`\n=== ALL CONTRACT TESTS COMPLETE ===`);
            return;
        }

        console.log("Calling runWeeklyBacktests...");
        runWeeklyBacktests();
        console.log(">>> Finished calling runWeeklyBacktests from 'end' handler.");
    })
    .on('error', (err) => {
        console.error(`Error reading CSV file ${config.inputFile}:`, err);
        process.exit(1);
    });

// --- Indicator Helper Functions ---
function SMA(arr, period, index) { if (!arr || index < period - 1 || arr.length <= index) return NaN; let sum = 0; let validCount = 0; for (let j = index - period + 1; j <= index; j++) { if (typeof arr[j] === 'number' && !isNaN(arr[j])) { sum += arr[j]; validCount++; } else { return NaN; } } return validCount === period ? sum / period : NaN; }
function RSI(arr, period, index) {
    if (!arr || index < period || arr.length <= index) return NaN;

    // Calculate price changes for the period
    let initialGains = 0, initialLosses = 0;
    let validDeltas = 0;

    // Calculate gains and losses for the period
    for (let j = index - period + 1; j <= index; j++) {
        if (j > 0 && typeof arr[j - 1] === 'number' && !isNaN(arr[j - 1]) && typeof arr[j] === 'number' && !isNaN(arr[j])) {
            const delta = arr[j] - arr[j - 1];
            if (delta > 0) {
                initialGains += delta;
            } else {
                initialLosses -= delta;
            }
            validDeltas++;
        } else {
            return NaN;
        }
    }

    if (validDeltas < period) return NaN;
    if (initialLosses === 0) return 100;
    if (initialGains === 0) return 0;

    // Calculate simple averages for the period (standard RSI calculation)
    const avgGain = initialGains / period;
    const avgLoss = initialLosses / period;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

// --- MACD Calculation ---
function EMA(data, period, index) {
    if (!data || !Array.isArray(data) || period <= 0 || index < 0 || index >= data.length) {
        return NaN;
    }

    if (index < period - 1) return NaN;

    const multiplier = 2 / (period + 1);

    // Calculate initial SMA for the first EMA value (using first 'period' values)
    let sum = 0;
    let count = 0;
    const startIdx = index - period + 1;

    for (let i = startIdx; i < startIdx + period; i++) {
        if (i >= 0 && i < data.length && !isNaN(data[i])) {
            sum += data[i];
            count++;
        }
    }

    if (count === 0) return NaN;
    let ema = sum / count;

    // If we're calculating EMA for the exact period boundary, return SMA
    if (index === period - 1) {
        return ema;
    }

    // Calculate EMA iteratively from period boundary to target index
    for (let i = period; i <= index; i++) {
        if (i >= 0 && i < data.length && !isNaN(data[i])) {
            ema = (data[i] * multiplier) + (ema * (1 - multiplier));
        } else {
            return NaN;
        }
    }

    return ema;
}

function MACD(data, fastPeriod, slowPeriod, signalPeriod, index) {
    if (!data || !Array.isArray(data) || index < 0 || index >= data.length) {
        return { macd: NaN, signal: NaN, histogram: NaN };
    }

    const fastEMA = EMA(data, fastPeriod, index);
    const slowEMA = EMA(data, slowPeriod, index);

    if (isNaN(fastEMA) || isNaN(slowEMA)) {
        return { macd: NaN, signal: NaN, histogram: NaN };
    }

    const macdLine = fastEMA - slowEMA;

    // For signal line, we need to calculate EMA of MACD values
    // This is a simplified approach - in practice you'd need to store MACD history
    return {
        macd: macdLine,
        signal: NaN, // Simplified - signal line calculation would need MACD history
        histogram: NaN
    };
}
function WMA(arr, period, index) { if (!arr || index < period - 1 || arr.length <= index) return NaN; let weightedSum = 0, weightSum = 0, validCount = 0; for (let j = 0; j < period; j++) { const idx = index - j, weight = period - j; if (idx < 0) return NaN; if (typeof arr[idx] === 'number' && !isNaN(arr[idx])) { weightedSum += arr[idx] * weight; weightSum += weight; validCount++; } else { return NaN; } } return validCount === period ? weightedSum / weightSum : NaN; }

// --- Other Helper Functions ---
function candlestickColor(candle) { if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) return 'invalid'; return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji'; }
function getWeekIdentifier(timestamp) {
    // Handle both string and number timestamps
    const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp * 1000);
    if (isNaN(date.getTime())) { return "InvalidDate"; }
    const year = date.getUTCFullYear();
    const startOfYear = new Date(Date.UTC(year, 0, 1));
    if (isNaN(startOfYear.getTime())) { return "InvalidYearStartDate"; }
    const timeDiff = date.getTime() - startOfYear.getTime();
    if (isNaN(timeDiff)) { return "InvalidTimeDiff"; }
    const daysPassed = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const firstDayOfYear = startOfYear.getUTCDay();
    const weekNo = Math.floor((daysPassed + firstDayOfYear) / 7) + 1;
    if (isNaN(weekNo) || weekNo < 1 || weekNo > 53) { return "InvalidWeekNo"; }
    return `${year}-W${weekNo.toString().padStart(2, '0')}`;
}
function getDayIdentifier(timestamp) {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp * 1000);
    return date.toISOString().slice(0, 10);
}

// --- Metrics ---
function calculateSharpe(weeklyReturns, riskFreeRate = 0) { const validReturns = weeklyReturns.filter(r => typeof r === 'number' && isFinite(r)); if (!validReturns || validReturns.length < 2) return 'N/A'; const numWeeks = validReturns.length; const meanWeeklyReturn = validReturns.reduce((a, b) => a + b, 0) / numWeeks; if (isNaN(meanWeeklyReturn)) return 'N/A'; const variance = validReturns.map(r => Math.pow(r - meanWeeklyReturn, 2)).reduce((a, b) => a + b, 0) / (numWeeks - 1); const stdDevWeeklyReturn = Math.sqrt(variance); if (isNaN(stdDevWeeklyReturn) || stdDevWeeklyReturn === 0) return 'N/A'; const annualizedMeanReturn = meanWeeklyReturn * 52, annualizedStdDev = stdDevWeeklyReturn * Math.sqrt(52); const sharpeRatio = (annualizedMeanReturn - (riskFreeRate || 0)) / annualizedStdDev; return isNaN(sharpeRatio) ? 'N/A' : sharpeRatio.toFixed(2); }
function calculateDailyStats(dailyPnLMap) { if (!dailyPnLMap || dailyPnLMap.size === 0) { return { avgDailyPnL: '0.00', stdDevDailyPnL: '0.00', winDayRate: 'N/A' }; } const values = Array.from(dailyPnLMap.values()), n = values.length; const totalPnl = values.reduce((s, p) => s + p, 0), avg = totalPnl / n; const winDays = values.filter(p => p > 0).length, winRate = `${(winDays / n * 100).toFixed(1)}%`; const variance = values.map(p => Math.pow(p - avg, 2)).reduce((s, sq) => s + sq, 0) / n; return { avgDailyPnL: avg.toFixed(2), stdDevDailyPnL: Math.sqrt(variance).toFixed(2), winDayRate: winRate }; }
function calculateTradeMetrics(tradeLog) { let winAmt=0, lossAmt=0, winCt=0, lossCt=0, duration=0, tradeCt=0, maxCont=0; if (!tradeLog || tradeLog.length === 0) { return { avgWinDollar: '0.00', avgLossDollar: '0.00', avgDurationBars: 'N/A', maxContractsHit: 0 }; } for (const trade of tradeLog) { const pnl=parseFloat(trade.PnL_Net), dur=parseInt(trade.Duration), cont=parseInt(trade.Contracts)||1; if(!isNaN(pnl)){ if(pnl>0){winAmt+=pnl;winCt++;} else if(pnl<0){lossAmt+=Math.abs(pnl);lossCt++;} } if(!isNaN(dur)&&dur>0){duration+=dur;} tradeCt++; maxCont=Math.max(maxCont,cont); } return { avgWinDollar:winCt>0?(winAmt/winCt).toFixed(2):'0.00', avgLossDollar:lossCt>0?(lossAmt/lossCt).toFixed(2):'0.00', avgDurationBars:tradeCt>0&&duration>0?(duration/tradeCt).toFixed(1):'N/A', maxContractsHit:maxCont }; }

// --- Results Aggregation ---
function aggregateResults(weeklyResults, runLabel) { if (!weeklyResults || weeklyResults.length === 0) return []; const aggData = { ParamsKey: runLabel, SL: weeklyResults[0]?.SL, TP: weeklyResults[0]?.TP, Trail: weeklyResults[0]?.Trail, MinTP_Tested: weeklyResults[0]?.MinTP, RiskPct_Tested: weeklyResults[0]?.RiskPct, FixedContracts_Tested: weeklyResults[0]?.FixedContracts, Latency_Tested: weeklyResults[0]?.LatencyBars, Commission_Tested: weeklyResults[0]?.Commission, Slippage_Tested: weeklyResults[0]?.Slippage, TotalPnL: 0, TotalTrades: 0, TotalWins: 0, TotalLosses: 0, TotalGrossProfit: 0, TotalGrossLoss: 0, MaxWeeklyDD: 0, WeeksRun: 0, ProfitableWeeks: 0, SumAvgATR: 0, AtrValidWeeks: 0, TotalExitCounts: { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, hold_expired: 0, end_of_period: 0, opposing_full_entry: 0 } }; for (const result of weeklyResults) { if (!result) continue; aggData.TotalPnL += result.TotalPnL||0; aggData.TotalTrades += result.Trades||0; aggData.TotalWins += result.Wins||0; aggData.TotalLosses += result.Losses||0; aggData.TotalGrossProfit += (result.AvgWin||0)*(result.Wins||0); aggData.TotalGrossLoss += (result.AvgLoss||0)*(result.Losses||0); aggData.MaxWeeklyDD = Math.max(aggData.MaxWeeklyDD, result.MaxDDWeekly || 0); aggData.WeeksRun++; if (typeof result.AvgATR === 'number' && !isNaN(result.AvgATR)) { aggData.SumAvgATR += result.AvgATR; aggData.AtrValidWeeks++; } if (result.TotalPnL > 0) { aggData.ProfitableWeeks++; } if (result.ExitCounts) { for (const r in result.ExitCounts) { aggData.TotalExitCounts[r] = (aggData.TotalExitCounts[r] || 0) + result.ExitCounts[r]; } } } const totalTradesCalc = aggData.TotalWins + aggData.TotalLosses; const winRate = totalTradesCalc > 0 ? (aggData.TotalWins / totalTradesCalc * 100) : 0; const pf = aggData.TotalGrossLoss > 0 ? (aggData.TotalGrossProfit / aggData.TotalGrossLoss) : (aggData.TotalGrossProfit > 0 ? 99999 : 0); const avgPnlWk = aggData.WeeksRun > 0 ? aggData.TotalPnL / aggData.WeeksRun : 0; const avgAtr = aggData.AtrValidWeeks > 0 ? aggData.SumAvgATR / aggData.AtrValidWeeks : NaN; let exitSum = "N/A"; if (totalTradesCalc > 0) { exitSum = Object.entries(aggData.TotalExitCounts).filter(([_, ct]) => ct > 0).map(([r, ct]) => `${r}: ${(ct / totalTradesCalc * 100).toFixed(1)}%`).join(', '); } return [{ Params: aggData.ParamsKey, TotalPnL: aggData.TotalPnL, AvgPnL_Week: avgPnlWk, WinRate: `${winRate.toFixed(2)}%`, ProfitFactor: pf === 99999 ? 'Infinity' : pf.toFixed(2), Trades: aggData.TotalTrades, Weeks: aggData.WeeksRun, WinWeeks: `${aggData.ProfitableWeeks}/${aggData.WeeksRun} (${aggData.WeeksRun > 0 ? (aggData.ProfitableWeeks/aggData.WeeksRun*100).toFixed(1) : '0.0'}%)`, AvgATR: avgAtr, MaxWeeklyDD: `-${aggData.MaxWeeklyDD.toFixed(config.pricePrecision)}`, Exits: exitSum, SL_Run: aggData.SL, TP_Run: aggData.TP, Trail_Run: aggData.Trail, MinTP_Run: aggData.MinTP_Tested, RiskPct_Run: aggData.RiskPct_Tested, FixedContracts_Tested: aggData.FixedContracts_Tested, Latency_Tested: aggData.Latency_Tested, Commission_Run: aggData.Commission_Tested, Slippage_Run: aggData.Slippage_Tested }]; }

// --- Results Display ---
function processAndDisplayAggregatedResults(resultsWithOverallMetrics) { if (!resultsWithOverallMetrics || resultsWithOverallMetrics.length === 0) { console.log("No aggregated results to display."); return; } console.log("\n--- AGGREGATED RESULTS SUMMARY ---"); const columns = [ "Params", "TotalPnL", "AvgPnL_Week", "WinRate", "ProfitFactor", "Trades", "AvgWinDollar", "AvgLossDollar", "MaxDD_Overall", "Sharpe_Ann", "AvgTradeDur_Bars", "MaxContractsHit", "WinDayRate", "AvgDailyPnL", "StdDevDailyPnL", "Weeks", "WinWeeks", "AvgATR", "Exits", "FixedContracts_Run", "LatencyTested", "Commission_Run", "Slippage_Run" ]; const displayData = resultsWithOverallMetrics.map(row => { const newRow = {}; columns.forEach(col => { let value = row[col]; if (typeof value === 'number') { if (['TotalPnL', 'AvgPnL_Week', 'AvgWinDollar', 'AvgLossDollar', 'AvgDailyPnL', 'StdDevDailyPnL'].includes(col)) { value = value.toFixed(2); } else if (col === 'ProfitFactor' && value !== Infinity && value !== 99999) { value = value.toFixed(2); } else if (col === 'AvgATR') { value = isNaN(value) ? 'N/A' : value.toFixed(4); } else if (col === 'Sharpe_Ann') { value = (isNaN(value) || !isFinite(value)) ? 'N/A' : value.toFixed(2); } } if (col === 'MaxDD_Overall' && typeof value === 'string' && value !== 'N/A' && !value.startsWith('-')) { const num = parseFloat(value); value = isNaN(num) ? 'N/A' : `-${num.toFixed(2)}`; } if (col === 'ProfitFactor' && value === 99999) { value = 'Infinity'; } if (col === 'FixedContracts_Run') value = row['FixedContracts_Tested']; if (col === 'LatencyTested') value = row['Latency_Tested']; newRow[col] = (value !== undefined && value !== null) ? value : 'N/A'; }); return newRow; }); if (displayData.length > 1) { displayData.sort((a, b) => { try { const pnlA=Number(String(a.TotalPnL).replace(/[^0-9.-]+/g,""))||-Infinity; const pnlB=Number(String(b.TotalPnL).replace(/[^0-9.-]+/g,""))||-Infinity; return pnlB - pnlA; } catch (e) { return 0; } }); } console.table(displayData, columns); }

// --- Save Trade Log ---
function saveTradeLog(tradeLog, filename) { if (!tradeLog || tradeLog.length === 0) { console.log(`Skipping save for ${filename}: No trades.`); return; } try { const headers = Object.keys(tradeLog[0]); if (!headers || headers.length === 0) { console.warn(`Cannot save ${filename}: No headers.`); return; } const headerString = headers.join(';'); const rows = tradeLog.map(trade => headers.map(h => String(trade[h] ?? '').replace(/;/g, ',').replace(/\n/g, ' ')).join(';')); fs.writeFileSync(path.join(outputDir, filename), [headerString, ...rows].join('\n')); console.log(`Trade log saved to: ${path.join(outputDir, filename)}`); } catch (error) { console.error(`\nError saving trade log ${filename}:`, error); } }

// --- 2) Global Indicator Calculation ---
function computeGlobalIndicators(candles) {
    console.log("Computing global indicators...");
    if (!candles || !Array.isArray(candles) || candles.length === 0) {
        throw new Error("computeGlobalIndicators invalid candle data.");
    }

    // Debug: Check first candle structure
    console.log("First candle structure:", JSON.stringify(candles[0], null, 2));

    if (!candles[0]?.close) {
        throw new Error("computeGlobalIndicators invalid candle structure.");
    }

    // FIXED: Define periods first
    const wma50Period = config.strategy?.wma50Period || config.wma50Period || 50;
    const wma200Period = config.strategy?.wma200Period || config.wma200Period || 200;

    // FIXED: Use typical price (HLC/3) for both moving averages instead of close only
    const typicalPrices = candles.map(c => c && !isNaN(c.high) && !isNaN(c.low) && !isNaN(c.close) ? (c.high + c.low + c.close) / 3 : NaN);

    // FIXED: 200 should be WMA (Weighted Moving Average), not SMA
    const wma200Arr = wma200Period > 0 ? typicalPrices.map((_, i) => WMA(typicalPrices, wma200Period, i)) : new Array(candles.length).fill(NaN);

    // FIXED: 50 WMA should also use typical price, not close
    const wma50Arr = wma50Period > 0 ? typicalPrices.map((_, i) => WMA(typicalPrices, wma50Period, i)) : new Array(candles.length).fill(NaN);

    // RSI calculation
    const closes = candles.map(c => c?.close);
    const rsiPeriod = config.strategy?.rsiPeriod || config.rsiPeriod || 14;
    const rsiMaPeriod = config.strategy?.rsiMaPeriod || config.rsiMaPeriod || 8;
    const rsiArr = rsiPeriod > 0 ? closes.map((_, i) => RSI(closes, rsiPeriod, i)) : new Array(candles.length).fill(NaN);
    const rsiMaArr = rsiMaPeriod > 0 && rsiPeriod > 0 ? rsiArr.map((_, i) => SMA(rsiArr, rsiMaPeriod, i)) : new Array(candles.length).fill(NaN);

    // MACD calculation (simplified - just fast EMA - slow EMA)
    let macdLineArr = new Array(candles.length).fill(NaN);

    if (config.useMACDFilter && config.macdFastLength > 0 && config.macdSlowLength > 0) {
        // Calculate MACD line iteratively for better performance
        const fastMultiplier = 2 / (config.macdFastLength + 1);
        const slowMultiplier = 2 / (config.macdSlowLength + 1);

        let fastEMA = NaN;
        let slowEMA = NaN;

        for (let i = 0; i < candles.length; i++) {
            const price = closes[i];
            if (isNaN(price)) continue;

            // Initialize EMAs with first valid price
            if (isNaN(fastEMA)) {
                fastEMA = price;
                slowEMA = price;
                continue;
            }

            // Update EMAs
            fastEMA = (price * fastMultiplier) + (fastEMA * (1 - fastMultiplier));
            slowEMA = (price * slowMultiplier) + (slowEMA * (1 - slowMultiplier));

            // Calculate MACD line (fast - slow)
            macdLineArr[i] = fastEMA - slowEMA;
        }
    }

    // ATR calculation
    const trs = [];
    for (let i = 0; i < candles.length; i++) {
        const cur = candles[i];
        if (!cur) {
            trs.push(NaN);
            continue;
        }
        if (i === 0) {
            if (!isNaN(cur.high) && !isNaN(cur.low)) {
                trs.push(cur.high - cur.low);
            } else {
                trs.push(NaN);
            }
            continue;
        }
        const prev = candles[i - 1];
        if (!prev || isNaN(prev.close) || isNaN(cur.high) || isNaN(cur.low)) {
            trs.push(NaN);
            continue;
        }
        trs.push(Math.max(cur.high - cur.low, Math.abs(cur.high - prev.close), Math.abs(cur.low - prev.close)));
    }
    const atrs = config.atrPeriod > 0 ? trs.map((_, i) => SMA(trs, config.atrPeriod, i)) : new Array(trs.length).fill(NaN);

    // Assign all indicators to candles
    candles.forEach((c, i) => {
        if (c) {
            c.wma200 = wma200Arr[i];
            c.wma50 = wma50Arr[i];
            c.rsi = rsiArr[i];
            c.rsiMa = rsiMaArr[i];
            c.atr = (i >= config.atrPeriod - 1 && i < atrs.length) ? atrs[i] : NaN;
            c.macd = macdLineArr[i]; // Add MACD line to candle data
        }
    });

    console.log("Global indicators computed.");
}

// --- 3) Weekly Data Segmentation ---
function segmentDataIntoWeeks(candlesWithIndicators) {
    console.log("Segmenting data into weeks (using simple Week definition)...");
    if (!candlesWithIndicators || !Array.isArray(candlesWithIndicators)) {
        console.error("Error: segmentDataIntoWeeks received invalid input.");
        return {};
    }
    const weeklyData = {};
    let invalidWeekIdCount = 0;
    let validWeekIdCount = 0;

    // Debug: Check first few candles
    console.log("First 3 candles for week segmentation:");
    for (let i = 0; i < Math.min(3, candlesWithIndicators.length); i++) {
        const candle = candlesWithIndicators[i];
        console.log(`Candle ${i}: timestamp=${candle.timestamp}, type=${typeof candle.timestamp}`);
        const weekId = getWeekIdentifier(candle.timestamp);
        console.log(`Week ID: ${weekId}`);
    }

    for (const candle of candlesWithIndicators) {
        if (!candle || (typeof candle.timestamp === 'number' && isNaN(candle.timestamp))) {
            continue;
        }
        const weekId = getWeekIdentifier(candle.timestamp);
        if (!weekId || typeof weekId !== 'string' || weekId.includes("Invalid") || weekId.includes("NaN")) {
            invalidWeekIdCount++;
            continue;
        } else {
            validWeekIdCount++;
            if (!weeklyData[weekId]) {
                weeklyData[weekId] = [];
            }
            weeklyData[weekId].push(candle);
        }
    }
    if (invalidWeekIdCount > 0) {
        console.warn(`Warning: Skipped ${invalidWeekIdCount} candles due to invalid week ID generation.`);
    }
    if (validWeekIdCount === 0 && candlesWithIndicators.length > 0) {
        console.error("Error: No valid week IDs generated for any candles!");
    }
    console.log(`Segmented data into ${Object.keys(weeklyData).length} weeks.`);
    const sortedWeeks = Object.keys(weeklyData).sort();
    const sortedWeeklyData = {};
    for(const weekId of sortedWeeks){
        if (weeklyData[weekId]?.length > 0) sortedWeeklyData[weekId] = weeklyData[weekId];
    }
    return sortedWeeklyData;
}

// --- 4) Main Workflow: Run Backtests Weekly + Overall Metrics ---
function runWeeklyBacktests() {
    console.log(">>> runWeeklyBacktests function entered.");
    try {
        console.log("Starting runWeeklyBacktests try block...");

        // Use contract-specific data if available, otherwise use all candles
        const candlesToUse = global.currentContractCandles || global.allCandles;
        const contractName = global.currentContractName || 'ALL';

        if (global.currentContractName) {
            console.log(`Processing contract: ${contractName} (${candlesToUse.length} candles)`);
        }

        computeGlobalIndicators(candlesToUse);
        console.log("Finished computeGlobalIndicators.");
        const weeklySegmentedData = segmentDataIntoWeeks(candlesToUse);
        console.log("Finished segmentDataIntoWeeks.");

        if (typeof weeklySegmentedData !== 'object' || weeklySegmentedData === null) { console.error("Error: segmentDataIntoWeeks did not return a valid object."); return; }
        const totalWeeksAllData = Object.keys(weeklySegmentedData).length;
        console.log(`Total weeks identified: ${totalWeeksAllData}`);
        if (totalWeeksAllData === 0) { console.error("Error: No weeks identified for backtesting. Exiting."); return; }

        allRunResults = [];
        let runFullTradeLog = [];

        // Determine Run Type and Parameter Sets
        console.log("Determining run type and parameters...");
        const isAdaptive = config.isAdaptiveRun === true;
        const isCostGridTest = config.costGrid?.length > 0;
        const isRiskGridTest = config.riskPercentGrid?.length > 0;
        const isFixedContractsGridTest = config.fixedContractsGrid?.length > 0;
        const isParameterGridTest = !isAdaptive && ( (Array.isArray(config.slFactors) && config.slFactors.length > 0) || (Array.isArray(config.tpFactors) && config.tpFactors.length > 0) || (Array.isArray(config.trailFactors) && config.trailFactors.length > 0) );
        const isTPOptimizationTest = config.isTPOptimizationRun === true;
        let parameterSets = [];
         console.log(` - Flags: isAdaptive=${isAdaptive}, isCostGrid=${isCostGridTest}, isRiskGrid=${isRiskGridTest}, isFixedContractsGrid=${isFixedContractsGridTest}, isParameterGrid=${isParameterGridTest}, isTPOptimization=${isTPOptimizationTest}`);

        // Build Parameter Sets
        console.log("Attempting to build parameter sets...");

        if (isTPOptimizationTest) {
            console.log(" -> Detected TP Optimization Test condition.");
            const tpConfigs = generateTPOptimizationConfigs();
            console.log(` -> Testing ${tpConfigs.length} TP configurations...`);

            // Create parameter sets for each TP configuration
            parameterSets = tpConfigs.map(tpConfig => ({
                slFactor: 0,
                tpFactor: 0,
                trailFactor: 0,
                tpConfigName: tpConfig.name,
                tpConfig: tpConfig.config,
                isAdaptive: false,
                riskPercent: config.riskPercent || 0,
                fixedContracts: config.fixedContracts || 1,
                commission: config.commissionPerContract,
                slippage: config.slippagePoints,
                latencyDelayBars: config.latencyDelayBars,
                fixedTp: 0 // Will use dynamic TP
            }));
        } else if (isParameterGridTest) {
            console.log(" -> Detected Parameter Grid Test condition."); parameterSets = getParameterCombinations(); if (!Array.isArray(parameterSets)) { console.error("FATAL: Param gen failed!"); parameterSets = [];} else if (parameterSets.length === 0) { console.error("PARAM GRID ERROR: No valid combinations generated."); } else { console.log(` -> Running FIXED Parameter Grid Test (${parameterSets.length} combinations)...`); }
        } else if (isFixedContractsGridTest) { console.log(" -> Building Fixed Contracts Grid Sets..."); const baseParamsList = getParameterCombinations(); if (isAdaptive || baseParamsList.length !== 1) { console.error("GRID ERROR: Contracts Grid needs single base params."); return; } const baseParams = baseParamsList[0]; parameterSets = config.fixedContractsGrid.map(c => typeof c==='number'&&c>0 ? {...baseParams, fixedContracts:c, isAdaptive:false, riskPercent:0, commission:config.commissionPerContract, slippage:config.slippagePoints, latencyDelayBars:config.latencyDelayBars, fixedTp:config.fixedTpPoints} : null).filter(p=>p); if (parameterSets.length === 0) { console.error("GRID ERROR: No valid contract sizes."); } }
        else if (isCostGridTest) { console.log(" -> Building Cost Grid Sets..."); const baseParamsList = getParameterCombinations(); if (isAdaptive || baseParamsList.length !== 1) { console.error("GRID ERROR: Cost Grid needs single base params."); return; } const baseParams = baseParamsList[0]; const riskP = config.riskPercent||0, contracts = riskP>0 ? 0 : (config.fixedContracts||1); parameterSets = config.costGrid.map(c => ({...baseParams, commission:c.commission, slippage:c.slippage, isAdaptive:false, riskPercent:riskP, fixedContracts:contracts, latencyDelayBars:config.latencyDelayBars, fixedTp:config.fixedTpPoints })); }
        else if (isRiskGridTest) { console.log(" -> Building Risk Grid Sets..."); let baseParamsList; if (isAdaptive) { baseParamsList = [{ isAdaptive: true, SL: 'Adaptive', TP: 'Adaptive', Trail: 'Adaptive', fixedTp: config.fixedTpPoints || 0 }]; } else { baseParamsList = getParameterCombinations(); if (baseParamsList.length === 0) { console.error("GRID ERROR: No base params for Risk Grid."); return; } if (baseParamsList.length > 1) console.warn("WARN: Multiple base params found for Risk Grid."); baseParamsList = [baseParamsList[0]]; baseParamsList = baseParamsList.map(p => ({...p, fixedTp: config.fixedTpPoints || 0, isAdaptive: false})); } parameterSets = []; baseParamsList.forEach(bp => config.riskPercentGrid.forEach(r => typeof r==='number'&&r>0 ? parameterSets.push({...bp, riskPercent:r, commission:config.commissionPerContract, slippage:config.slippagePoints, fixedContracts:0, latencyDelayBars:config.latencyDelayBars}) : console.warn(`Skip invalid risk %: ${r}`))); if (parameterSets.length === 0) { console.error("GRID ERROR: No valid risk percentages."); } }
         else { // Single Run
            console.log(" -> Building Single Run Set..."); let baseParams; if (isAdaptive) { baseParams = { isAdaptive: true, SL: 'Adaptive', TP: 'Adaptive', Trail: 'Adaptive' }; console.log(" -> Mode: SINGLE ADAPTIVE"); } else { const bpl = getParameterCombinations(); if (!Array.isArray(bpl) || bpl.length === 0) { console.error("RUN ERROR: No valid SL/TP/Trail params found for Single Run."); return; } if (bpl.length > 1) console.warn("WARN: Multiple base params found for Single Run."); baseParams = bpl[0]; console.log(" -> Mode: SINGLE FIXED"); } const riskP = config.riskPercent||0, contracts = riskP>0 ? 0 : (config.fixedContracts||1); parameterSets = [{ ...baseParams, fixedTp: config.fixedTpPoints||0, riskPercent: riskP, fixedContracts: contracts, commission: config.commissionPerContract, slippage: config.slippagePoints, latencyDelayBars: config.latencyDelayBars, isAdaptive: isAdaptive }];
        }

        console.log(` -> Generated ${parameterSets ? parameterSets.length : 'undefined'} parameter sets (final assignment).`);
        parameterSets = Array.isArray(parameterSets) ? parameterSets : [];
        if (parameterSets.length === 0) { console.error("Error: No parameter sets to process after setup."); }


        console.log(`Starting parameter set loop (processing ${parameterSets.length} sets)...`);
        // --- Loop through parameter sets ---
        for (const params of parameterSets) {
             // Set current TP configuration if this is a TP optimization run
             if (params.tpConfig) {
                 CURRENT_TP_CONFIG = params.tpConfig;
                 console.log(`\nTesting TP Config: ${params.tpConfigName}`);
                 console.log(`TP Values: ${JSON.stringify(params.tpConfig)}`);
             } else {
                 CURRENT_TP_CONFIG = null; // Use default TP values
             }

             config.currentRunParams = { ...params }; config.fixedTpPoints = config.fixedTpPoints || 0; config.currentRiskPercent = config.riskPercent || 0; config.currentFixedContracts = config.fixedContracts || 1; config.commissionPerContract = config.commissionPerContract; config.slippagePoints = config.slippagePoints; config.latencyDelayBars = config.latencyDelayBars || 0;
             const isCurrentRunAdaptive = params.isAdaptive ?? false;
             const trailStepSizeMultiplier = params.trailStepSizeMultiplier || config.trailStepSizeMultiplier;
             const atrRangeLabel = (params.minAtrEntry !== undefined || params.maxAtrEntry !== undefined) ?
                 `ATR(${params.minAtrEntry || 0}-${params.maxAtrEntry || 999})` : '';
             const baseLabel = params.tpConfigName ? params.tpConfigName :
                 (isCurrentRunAdaptive ? 'Adaptive' :
                  (params.useFixedPoints ?
                   `SL=${params.fixedSlPoints},TP=${params.fixedTpPoints},Tr=${params.trailFactor},TrStep=${trailStepSizeMultiplier}${atrRangeLabel ? ',' + atrRangeLabel : ''}` :
                   `SL=${params.slFactor},TP=${params.tpFactor},Tr=${params.trailFactor},TrStep=${trailStepSizeMultiplier}${atrRangeLabel ? ',' + atrRangeLabel : ''}`));
             let additions = [];
             if (config.fixedTpPoints > 0) additions.push(`MinTP=${config.fixedTpPoints}`);
             if (config.currentRiskPercent > 0) {
                 additions.push(`Risk=${(config.currentRiskPercent * 100).toFixed(1)}%`);
                 if(config.maxContracts < Infinity) additions.push(`Cap=${config.maxContracts}`);
             } else if (config.currentFixedContracts >= 1) {
                 additions.push(`Contracts=${config.currentFixedContracts}`);
             } else {
                 config.currentFixedContracts = 1;
                 additions.push(`Contracts=1`);
             }
             if (config.latencyDelayBars > 0) additions.push(`Latency=${config.latencyDelayBars}bar`);
             if (isCostGridTest || config.commissionPerContract !== 1.00 || config.slippagePoints !== 0.25) {
                 const rtSlipCost=config.slippagePoints*config.pointValue, totalRT=config.commissionPerContract+rtSlipCost;
                 additions.push(`Costs(C=${config.commissionPerContract.toFixed(2)}/S=${config.slippagePoints.toFixed(2)}pt=$${totalRT.toFixed(2)}RT)`);
             }
             const runLabel = baseLabel + (additions.length > 0 ? ', ' + additions.join(', ') : '');
             console.log(`\nTesting Params: ${runLabel}`);
             let currentEquity = config.initialBalance, overallPeakBalance = config.initialBalance, overallMaxDrawdown = 0; let weeklyReturns = [], weeklyResultsForParamSet = []; runFullTradeLog = []; let dailyPnLMap = new Map(), weekCounter = 0;
             const weeklyDataCopy = JSON.parse(JSON.stringify(weeklySegmentedData));
             const weeksToProcess = Object.entries(weeklyDataCopy); const totalWeeksToRun = weeksToProcess.length; if (totalWeeksToRun === 0) { console.warn(`Warning: No weeks in copied data. Skipping ${runLabel}.`); continue; }

             for (const [weekIdentifier, weekCandles] of weeksToProcess) {
                weekCounter++; process.stdout.write(` Processing Week ${weekCounter}/${totalWeeksToRun}: ${weekIdentifier}\r`);
                const wma200Period = config.strategy?.wma200Period || config.wma200Period || 200;
               const wma50Period = config.strategy?.wma50Period || config.wma50Period || 50;
               const rsiPeriod = config.strategy?.rsiPeriod || config.rsiPeriod || 14;
               const rsiMaPeriod = config.strategy?.rsiMaPeriod || config.rsiMaPeriod || 8;
               const atrPeriod = config.atrPeriod || 14;
               const indPeriods = [wma200Period,wma50Period,rsiPeriod,rsiMaPeriod,atrPeriod]; const validP = indPeriods.map(Number).filter(p=>p>0&&!isNaN(p)); const maxP = validP.length>0?Math.max(...validP):0; const minLookback = Math.max(maxP, 3); if (weekCandles.length < minLookback) { continue; }
                const startBalance = currentEquity; const backtestResult = backtest(weekCandles, {
                    initialBalance: currentEquity,
                    overallPeakBalance: overallPeakBalance,
                    overallMaxDrawdown: overallMaxDrawdown,
                    slFactor: params.slFactor,
                    tpFactor: params.tpFactor,
                    trailFactor: params.trailFactor,
                    isAdaptiveRun: isCurrentRunAdaptive,
                    dailyPnLMap: dailyPnLMap,
                    fixedSlPoints: params.fixedSlPoints,
                    fixedTpPoints: params.fixedTpPoints,
                    useFixedPoints: params.useFixedPoints,
                    immediateCandleExit: params.immediateCandleExit,
                    maxTimeExitMinutes: params.maxTimeExitMinutes,
                    minAtrEntry: params.minAtrEntry || config.minAtrEntry,
                    maxAtrEntry: params.maxAtrEntry || config.maxAtrEntry
                });
                currentEquity = backtestResult.finalBalance; overallPeakBalance = backtestResult.peakBalance; overallMaxDrawdown = backtestResult.maxDrawdown; dailyPnLMap = backtestResult.dailyPnLMap; if (startBalance > 0 && !isNaN(currentEquity) && !isNaN(startBalance)) { weeklyReturns.push((currentEquity - startBalance) / startBalance); } else if (startBalance <= 0) { weeklyReturns.push(0); } runFullTradeLog.push(...backtestResult.tradeLog); let atrSum = 0, atrCt = 0; for(const c of weekCandles) if(c && typeof c.atr === 'number' && !isNaN(c.atr) && c.atr > 0) { atrSum += c.atr; atrCt++; } const wkAvgAtr = atrCt > 0 ? (atrSum / atrCt) : NaN; const wkRegime = isNaN(wkAvgAtr) ? 'N/A' : (wkAvgAtr < atrThresholds.low_medium ? 'Low' : (wkAvgAtr > atrThresholds.medium_high ? 'High' : 'Medium')); weeklyResultsForParamSet.push({ Week: weekIdentifier, ParamsSpecific: runLabel, SL: (params.useFixedPoints ? params.fixedSlPoints : params.slFactor), TP: (params.useFixedPoints ? params.fixedTpPoints : params.tpFactor), Trail: params.trailFactor, MinTP: config.fixedTpPoints, RiskPct: config.currentRiskPercent, FixedContracts: config.currentFixedContracts, Commission: config.commissionPerContract, Slippage: config.slippagePoints, LatencyBars: config.latencyDelayBars, AvgATR: isNaN(wkAvgAtr)?NaN:parseFloat(wkAvgAtr.toFixed(4)), Regime: wkRegime, Trades: backtestResult.tradesTaken, Wins: backtestResult.wins, Losses: backtestResult.losses, TotalPnL: backtestResult.totalPnL, WinRate: backtestResult.winRate, ProfitFactor: backtestResult.profitFactor, AvgWin: backtestResult.avgWin, AvgLoss: backtestResult.avgLoss, MaxDDWeekly: parseFloat(backtestResult.maxDrawdownWeekly.replace('-','')) || 0, ExitCounts: backtestResult.exitCounts });
             } process.stdout.write('\n');
             if (weeklyResultsForParamSet.length > 0) { const aggRes = aggregateResults(weeklyResultsForParamSet, runLabel)[0]; if (aggRes) { aggRes.MaxDD_Overall = `-${overallMaxDrawdown.toFixed(config.pricePrecision)}`; aggRes.Sharpe_Ann = calculateSharpe(weeklyReturns); const tradeMetrics = calculateTradeMetrics(runFullTradeLog); aggRes.AvgWinDollar = tradeMetrics.avgWinDollar; aggRes.AvgLossDollar = tradeMetrics.avgLossDollar; aggRes.AvgTradeDur_Bars = tradeMetrics.avgDurationBars; aggRes.MaxContractsHit = tradeMetrics.maxContractsHit; const dailyStats = calculateDailyStats(dailyPnLMap); aggRes.AvgDailyPnL = dailyStats.avgDailyPnL; aggRes.StdDevDailyPnL = dailyStats.stdDevDailyPnL; aggRes.WinDayRate = dailyStats.winDayRate; aggRes.FixedContracts_Run = aggRes.FixedContracts_Tested; aggRes.LatencyTested = aggRes.Latency_Tested; allRunResults.push(aggRes); } else { console.warn(`Warning: No aggregated result for ${runLabel}.`); } } else { console.warn(`Warning: No weekly results for ${runLabel}.`); }
        } console.log("Finished parameter set loop.");

        // Debug: Show filter breakdown
        console.log("\n=== ENTRY FILTER BREAKDOWN ===");
        console.log(`Total pattern checks: ${debugCounters.totalChecks.toLocaleString()}`);
        console.log(`Patterns found: ${debugCounters.patternFound.toLocaleString()} (${(debugCounters.patternFound/debugCounters.totalChecks*100).toFixed(2)}%)`);
        console.log(`Passed WMA filter: ${debugCounters.wmaFilter.toLocaleString()} (${(debugCounters.wmaFilter/debugCounters.patternFound*100).toFixed(2)}%)`);
        console.log(`Passed 200 WMA filter: ${debugCounters.wma200Filter.toLocaleString()} (${(debugCounters.wma200Filter/debugCounters.wmaFilter*100).toFixed(2)}%)`);
        if (config.useRSIFilter !== false) {
            console.log(`Passed RSI filter: ${debugCounters.rsiFilter.toLocaleString()} (${(debugCounters.rsiFilter/debugCounters.wma200Filter*100).toFixed(2)}%)`);
            console.log(`Passed MACD filter: ${debugCounters.macdFilter.toLocaleString()} (${(debugCounters.macdFilter/debugCounters.rsiFilter*100).toFixed(2)}%)`);
        } else {
            console.log(`RSI filter: DISABLED - testing MACD replacement`);
            console.log(`Passed MACD filter: ${debugCounters.macdFilter.toLocaleString()} (${(debugCounters.macdFilter/debugCounters.wma200Filter*100).toFixed(2)}%)`);
        }
        console.log(`Final entries: ${debugCounters.finalPass.toLocaleString()} (${(debugCounters.finalPass/debugCounters.macdFilter*100).toFixed(2)}%)`);
        console.log(`Overall conversion: ${(debugCounters.finalPass/debugCounters.totalChecks*100).toFixed(4)}%`);

        // HOURLY ANALYSIS: Aggregate hourly stats from all trade logs
        console.log("\n=== HOURLY WIN RATE ANALYSIS ===");
        let globalHourlyStats = {};
        for (let hour = 0; hour < 24; hour++) {
            globalHourlyStats[hour] = {
                totalTrades: 0,
                winningTrades: 0,
                losingTrades: 0,
                totalPnL: 0,
                grossProfit: 0,
                grossLoss: 0,
                winRate: 0,
                profitFactor: 0,
                avgWin: 0,
                avgLoss: 0
            };
        }

        // Aggregate hourly stats from all trades in runFullTradeLog
        if (runFullTradeLog && runFullTradeLog.length > 0) {
            for (const trade of runFullTradeLog) {
                if (trade.ExitTime) {
                    const exitTime = new Date(trade.ExitTime);
                    const hour = exitTime.getUTCHours();
                    const pnl = parseFloat(trade.PnL_Net) || 0;

                    globalHourlyStats[hour].totalTrades++;
                    globalHourlyStats[hour].totalPnL += pnl;

                    if (pnl > 0) {
                        globalHourlyStats[hour].winningTrades++;
                        globalHourlyStats[hour].grossProfit += pnl;
                    } else {
                        globalHourlyStats[hour].losingTrades++;
                        globalHourlyStats[hour].grossLoss += Math.abs(pnl);
                    }
                }
            }
        }

        // Calculate final hourly metrics
        for (let hour = 0; hour < 24; hour++) {
            const stats = globalHourlyStats[hour];
            if (stats.totalTrades > 0) {
                stats.winRate = (stats.winningTrades / stats.totalTrades) * 100;
                stats.profitFactor = stats.grossLoss > 0 ? (stats.grossProfit / stats.grossLoss) : (stats.grossProfit > 0 ? 99999 : 0);
                stats.avgWin = stats.winningTrades > 0 ? (stats.grossProfit / stats.winningTrades) : 0;
                stats.avgLoss = stats.losingTrades > 0 ? (stats.grossLoss / stats.losingTrades) : 0;
            }
        }

        // Display ALL 24 HOURS individually
        console.log("\n🕐 ALL 24 HOURS INDIVIDUAL PERFORMANCE:");
        console.log("Hour | Trades | Win% | Net P&L | Avg Win | Avg Loss | PF");
        console.log("-".repeat(65));

        for (let hour = 0; hour < 24; hour++) {
            const h = globalHourlyStats[hour];
            const hourStr = hour.toString().padStart(2, '0') + ':00 UTC';
            if (h.totalTrades > 0) {
                console.log(`${hourStr} | ${h.totalTrades.toString().padStart(6)} | ${h.winRate.toFixed(1).padStart(4)}% | ${h.totalPnL.toFixed(0).padStart(7)} | ${h.avgWin.toFixed(0).padStart(7)} | ${h.avgLoss.toFixed(0).padStart(8)} | ${h.profitFactor === 99999 ? 'Inf' : h.profitFactor.toFixed(2)}`);
            } else {
                console.log(`${hourStr} | ${h.totalTrades.toString().padStart(6)} | ${'N/A'.padStart(4)} | ${'0'.padStart(7)} | ${'N/A'.padStart(7)} | ${'N/A'.padStart(8)} | ${'N/A'}`);
            }
        }

        // Also show top 25 for quick reference
        console.log("\n🏆 TOP 25 PERFORMING HOURS (Min 5 trades):");
        console.log("Hour | Trades | Win% | Net P&L | Avg Win | Avg Loss | PF");
        console.log("-".repeat(65));

        const hoursByWinRate = [];
        for (let hour = 0; hour < 24; hour++) {
            if (globalHourlyStats[hour].totalTrades >= 5) {
                hoursByWinRate.push({
                    hour: hour,
                    ...globalHourlyStats[hour]
                });
            }
        }

        hoursByWinRate.sort((a, b) => b.winRate - a.winRate);

        for (let i = 0; i < Math.min(10, hoursByWinRate.length); i++) {
            const h = hoursByWinRate[i];
            const hourStr = h.hour.toString().padStart(2, '0') + ':00 UTC';
            console.log(`${hourStr} | ${h.totalTrades.toString().padStart(6)} | ${h.winRate.toFixed(1).padStart(4)}% | ${h.totalPnL.toFixed(0).padStart(7)} | ${h.avgWin.toFixed(0).padStart(7)} | ${h.avgLoss.toFixed(0).padStart(8)} | ${h.profitFactor === 99999 ? 'Inf' : h.profitFactor.toFixed(2)}`);
        }

        // Save hourly analysis
        const hourlyAnalysisFile = path.join(outputDir, `hourly_analysis_${new Date().toISOString().slice(0, 10)}.json`);
        fs.writeFileSync(hourlyAnalysisFile, JSON.stringify(globalHourlyStats, null, 2));
        console.log(`\n💾 Hourly analysis saved to: ${hourlyAnalysisFile}`);

        console.log("\n\n================ ALL BACKTESTS COMPLETE ================");
        processAndDisplayAggregatedResults(allRunResults);

        // Generate comprehensive hourly analysis
        const comprehensiveAnalysis = generateComprehensiveHourlyAnalysis(allRunResults);

        // Machine-readable summary for test harnesses
        try {
            console.log("\n=== MACHINE READABLE SUMMARY ===");
            console.log("BACKTEST_SUMMARY_JSON_START");
            // Emit aggregated results exactly as used by console.table
            console.log(JSON.stringify({ results: allRunResults }, null, 2));
            console.log("BACKTEST_SUMMARY_JSON_END");
        } catch (e) {
            console.warn('WARN: Failed to emit machine-readable summary:', e?.message);
        }


        try { const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); const resultsFilename = `aggregated_results_${timestamp}.json`; fs.writeFileSync(path.join(outputDir, resultsFilename), JSON.stringify(allRunResults, null, 2)); console.log(`\nAggregated results saved to: ${path.join(outputDir, resultsFilename)}`);

        // Save comprehensive analysis
        const analysisFile = `comprehensive_analysis_${timestamp}.json`;
        fs.writeFileSync(path.join(outputDir, analysisFile), JSON.stringify(comprehensiveAnalysis, null, 2));
        console.log(`Comprehensive analysis saved to: ${path.join(outputDir, analysisFile)}`);

        if (allRunResults.length > 0 && runFullTradeLog?.length > 0) { const lastRunLabel = allRunResults[allRunResults.length-1]?.Params || 'UnknownParams'; const safeLastRunLabel = lastRunLabel.replace(/[^a-zA-Z0-9_,-]/g, '_').substring(0, 60); const tradeLogFilename = `trade_log_LastRun_${safeLastRunLabel}_${timestamp}.csv`; saveTradeLog(runFullTradeLog, tradeLogFilename); } else { console.log("Skipping trade log save (no trades/runs)."); } } catch (error) { console.error("\nError saving results files:", error); }

    } catch (error) { console.error("Critical error during backtest run:", error); process.exit(1); }
} // --- End runWeeklyBacktests ---


// --- 8) Backtest Function ---
function backtest(candlesForPeriod, { initialBalance, overallPeakBalance, overallMaxDrawdown, slFactor, tpFactor, trailFactor, isAdaptiveRun, dailyPnLMap, fixedSlPoints, fixedTpPoints, useFixedPoints, immediateCandleExit, maxTimeExitMinutes, minAtrEntry, maxAtrEntry }) { const riskPercent = config.currentRiskPercent, fixedContracts = config.currentFixedContracts, maxContractsCap = config.maxContracts || Infinity, latencyDelay = config.latencyDelayBars || 0; let balance = initialBalance, wins = 0, losses = 0, trades = 0, pos = null, tradeIdCounter = 0; const completedTrades = []; let peakBalanceWeekly = initialBalance, maxDrawdownWeekly = 0, currentOverallPeak = overallPeakBalance, currentOverallMaxDD = overallMaxDrawdown; let grossProfit = 0, grossLoss = 0; let exitCounts = { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, hold_expired: 0, end_of_period: 0, time_1candle: 0, time_1candle_profit: 0, opposing_full_entry: 0, '200wma_break': 0 }; let currentDailyPnLMap = dailyPnLMap || new Map();

// Create params object for entryOK (available throughout the function)
const entryParams = { minAtrEntry, maxAtrEntry };

// HOURLY TRACKING: Initialize hourly statistics
let hourlyStats = {};
for (let hour = 0; hour < 24; hour++) {
    hourlyStats[hour] = {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalPnL: 0,
        grossProfit: 0,
        grossLoss: 0
    };
} const requiredLookback = 3, startIdx = requiredLookback; if (candlesForPeriod.length <= startIdx) { return { tradesTaken: 0, wins: 0, losses: 0, totalPnL: 0.0, winRate: 0.0, maxDrawdownWeekly: '-0.00', profitFactor: 0.0, avgWin: 0.0, avgLoss: 0.0, tradeLog: [], exitCounts, finalBalance: balance, peakBalance: currentOverallPeak, maxDrawdown: currentOverallMaxDD, dailyPnLMap: currentDailyPnLMap }; } for (let i = startIdx; i < candlesForPeriod.length; i++) { const c0=candlesForPeriod[i-3], c1=candlesForPeriod[i-2], c2=candlesForPeriod[i-1], c3=candlesForPeriod[i]; if (!c3) continue; // Time filter: Only trade during profitable hours
if (config.timeFilterEnabled && config.allowedTradingHours) {
    const currentHour = typeof c3.timestamp === 'string' ? new Date(c3.timestamp).getUTCHours() : new Date(c3.timestamp * 1000).getUTCHours();
    const isAllowedHour = config.allowedTradingHours.includes(currentHour);

    if (!isAllowedHour) {
        // Outside trading hours - manage existing position but don't enter new trades
        if (pos) {
            // Continue managing existing position
        } else {
            // Skip this candle for new entries
            continue;
        }
    }
}

// FIXED: Removed daily profit target that was causing early termination with large position sizes
const currentDay = getDayIdentifier(c3.timestamp);
// Daily P&L tracking for statistics only - no trading restrictions // Skip RSI validation if RSI is disabled
const rsiEnabled = config.useRSIFilter !== false && (config.strategy?.rsiPeriod || config.rsiPeriod) > 0 && (config.strategy?.rsiMaPeriod || config.rsiMaPeriod) > 0;
const rsiValid = !rsiEnabled || (!isNaN(c3.rsi) && !isNaN(c3.rsiMa));
if (isNaN(c3.wma50)||isNaN(c3.wma200)||!rsiValid) { if (pos) { /* ... manage pos ... */ } continue; }

// Debug: Track pattern detection and filtering
let debugPatternCount = 0;
let debugRsiFilterCount = 0;
let debugMaFilterCount = 0; let curSL, curTP, curTr, curReg; if (isAdaptiveRun) { const atr=c3.atr; curReg=(atr<atrThresholds.low_medium)?'Low':(atr>atrThresholds.medium_high?'High':'Medium'); curSL=adaptiveParams[curReg].slFactor; curTP=adaptiveParams[curReg].tpFactor; curTr=adaptiveParams[curReg].trailFactor; } else { curSL=slFactor; curTP=tpFactor; curTr=trailFactor; curReg='Fixed'; } const p3=detect3(c1,c2,c3); let pat=null, pType=null; if(p3){pat=p3;pType='three';debugPatternCount++;}



        if (!pos && pat && entryOK(pat, pType, c3, i, candlesForPeriod, entryParams)) { tradeIdCounter++; trades++; let entryP, atrE, entryTs, entryIdx = i; atrE = c3.atr; entryP = c3.close; entryTs = typeof c3.timestamp === 'string' ? new Date(c3.timestamp) : new Date(c3.timestamp * 1000); if(latencyDelay>0&&(i+latencyDelay)<candlesForPeriod.length){ const entryExecCandleIndex = i + latencyDelay; const entryExecCandle = candlesForPeriod[entryExecCandleIndex]; if (entryExecCandle && !isNaN(entryExecCandle.open)) { entryP = entryExecCandle.open; entryTs = typeof entryExecCandle.timestamp === 'string' ? new Date(entryExecCandle.timestamp) : new Date(entryExecCandle.timestamp * 1000); entryIdx = entryExecCandleIndex; } else {trades--;tradeIdCounter--;continue;} } else if(latencyDelay>0){trades--;tradeIdCounter--;continue;} if (typeof curSL !== 'number' || isNaN(curSL) || typeof curTP !== 'number' || isNaN(curTP) || typeof curTr !== 'number' || isNaN(curTr)) { console.error(`Invalid factors: SL=${curSL}, TP=${curTP}, Trail=${curTr}`); trades--; tradeIdCounter--; continue; }

// Check if we're using fixed points for SL/TP
let slDist, tpDist;
if (useFixedPoints) {
    // Use fixed point values directly
    slDist = fixedSlPoints;

    // Check if dynamic TP is enabled in config
    if (config.useDynamicTP) {
        // Use dynamic TP based on time of day (matching live trading)
        tpDist = getDynamicTpPoints(c3.timestamp, 'MNQ');
        console.log(`Using dynamic TP: SL=${slDist}, TP=${tpDist} (hour: ${new Date(c3.timestamp * 1000).getUTCHours()} UTC)`);
    } else {
        // Use fixed TP from grid
        tpDist = fixedTpPoints;
        console.log(`Using fixed TP: SL=${slDist}, TP=${tpDist}`);
    }
} else {
    // Use ATR-based values
    slDist = atrE * curSL;
    const atrTpDist = atrE * curTP;
    const fixTpDist = getDynamicTpPoints(c3.timestamp, 'MNQ'); // Use dynamic TP instead of fixed
    tpDist = Math.max(fixTpDist, atrTpDist);
} let cont;
                // Use dynamic position sizing if enabled
                if (config.useDynamicPositionSizing && positionSizer) {
                    // Get the timestamp
                    const timestamp = new Date(c3.timestamp * 1000);

                    // Determine ATR regime
                    const atrRegime = c3.atr < atrThresholds.low_medium ? 'Low' :
                                     (c3.atr > atrThresholds.medium_high ? 'High' : 'Medium');

                    // Get time score if time analysis is enabled
                    let timeScore = 0.5; // Default neutral score
                    if (config.useTimeAnalysis && timeAnalyzer) {
                        const timeScoreInfo = timeAnalyzer.getTimeScore(timestamp);
                        timeScore = timeScoreInfo.combinedScore;
                    }

                    // Get ML entry score if ML filter is enabled
                    let entryScore = 0.7; // Default score
                    if (config.useMLEntryFilter && mlFilter && config.useAdvancedMLFilter) {
                        // Create feature object for ML filter
                        const features = {
                            direction: pat,
                            atrRegime: atrRegime,
                            hourOfDay: timestamp.getUTCHours(),
                            dayOfWeek: timestamp.getUTCDay(),
                            rsi: c3.rsi,
                            rsiMa: c3.rsiMa,
                            close: c3.close,
                            wma50: c3.wma50,
                            patternType: pType
                        };

                        // Calculate entry score (without making the decision)
                        entryScore = mlFilter.calculateEntryScore(features) / mlFilter.entryThreshold;
                    }

                    // Update position sizer with current balance
                    positionSizer.updateBalance(balance);

                    // Calculate position size based on all factors
                    cont = positionSizer.calculatePositionSize({
                        currentATR: c3.atr,
                        regime: atrRegime,
                        timeScore: timeScore,
                        entryScore: entryScore
                    });
                }
                // Use volatility-based position sizing if enabled and dynamic sizing is not used
                else if (config.useVolatilityPositionSizing && riskPercent > 0 && balance > 0) {
                    cont = spreadUtils.calculateVolatilityBasedPositionSize(atrE, balance, riskPercent, config);
                }
                // Use risk-based position sizing if enabled and neither dynamic nor volatility sizing is used
                else if (riskPercent > 0 && balance > 0) {
                    const pv = config.pointValue;
                    const slpC = (config.slippagePoints || 0) * pv;
                    const rpc = (slDist * pv) + config.commissionPerContract + slpC;
                    cont = (rpc > 0) ? Math.min(maxContractsCap, Math.max(1, Math.floor((balance * riskPercent) / rpc))) : 1;
                }
                // Use fixed contracts as a fallback
                else {
                    cont = fixedContracts;
                }
                cont = Math.max(1, cont); const entryC = candlesForPeriod[entryIdx]; if (!entryC) { trades--; tradeIdCounter--; continue; } pos={ tradeId:tradeIdCounter, entryTimestamp:entryTs, dir:pat, entry:entryP, atr:atrE, tpDistance:tpDist, slDistance:slDist, stopLossPrice:(pat==='bullish')?entryP-slDist:entryP+slDist, trailStopPrice:(pat==='bullish')?entryP-(atrE*curTr):entryP+(atrE*curTr), trailFactor:curTr, entryAtrRegime:curReg, trailHigh:entryC.high, trailLow:entryC.low, entryBarIndex:entryIdx, currentBarIndex:entryIdx, tpType:useFixedPoints ? 'FixedPoints' : 'ATR', contracts:cont, immediateCandleExit: immediateCandleExit || null }; } if(pos){ if(i>pos.entryBarIndex){
                // Use enhanced position management if enabled, otherwise use standard
                // Get the current trail step size multiplier from config
                const trailStepSizeMultiplier = config.trailStepSizeMultiplier || 0.1;

                // Create a modified config with the current trail step size multiplier
                const modifiedConfig = {
                    ...config,
                    trailStepSizeMultiplier: trailStepSizeMultiplier
                };

                const exitInfo = config.useSteppedTrail || config.useAdaptiveSlippage ?
                    enhancedPM.enhancedManagePosition(pos, c3, i, candlesForPeriod, completedTrades, modifiedConfig, exitCounts) :
                    managePosition(pos, c3, i, candlesForPeriod, completedTrades, config, exitCounts, maxTimeExitMinutes, entryParams);

                if(exitInfo){
                    balance+=exitInfo.pnlNetTotal;
                    const day=getDayIdentifier(exitInfo.exitTimestamp.getTime()/1000);
                    currentDailyPnLMap.set(day,(currentDailyPnLMap.get(day)||0)+exitInfo.pnlNetTotal);

                    // HOURLY TRACKING: Update hourly stats when trade exits
                    const exitHour = exitInfo.exitTimestamp.getUTCHours();
                    hourlyStats[exitHour].totalTrades++;
                    hourlyStats[exitHour].totalPnL += exitInfo.pnlNetTotal;
                    if(exitInfo.pnlNetTotal > 0) {
                        hourlyStats[exitHour].winningTrades++;
                        hourlyStats[exitHour].grossProfit += exitInfo.pnlNetTotal;
                    } else {
                        hourlyStats[exitHour].losingTrades++;
                        hourlyStats[exitHour].grossLoss += Math.abs(exitInfo.pnlNetTotal);
                    }

                    if(exitInfo.pnlNetTotal>0){wins++;grossProfit+=exitInfo.pnlNetTotal;}else{losses++;grossLoss+=Math.abs(exitInfo.pnlNetTotal);}
                    peakBalanceWeekly=Math.max(peakBalanceWeekly,balance);
                    maxDrawdownWeekly=Math.max(maxDrawdownWeekly, peakBalanceWeekly-balance);
                    currentOverallPeak=Math.max(currentOverallPeak,balance);
                    currentOverallMaxDD=Math.max(currentOverallMaxDD, currentOverallPeak-balance);
                    pos=null;
                }
            } else {
                pos.trailHigh=Math.max(pos.trailHigh, c3.high);
                pos.trailLow=Math.min(pos.trailLow, c3.low);
            }
        } } if(pos){
        const lastC=candlesForPeriod[candlesForPeriod.length-1];
        if (!lastC) {
            console.warn("End of period check: Last candle missing!");
            pos = null;
        } else {
            const exitP=lastC.close, exitTs=typeof lastC.timestamp === 'string' ?
                new Date(lastC.timestamp) : new Date(lastC.timestamp*1000);
            pos.currentBarIndex=candlesForPeriod.length-1;

            // Use enhanced exit logic if enabled
            let exitInfo;
            if (config.useSteppedTrail || config.useAdaptiveSlippage) {
                // Get recent candles for spread estimation
                const lookbackStart = Math.max(0, candlesForPeriod.length - 11);
                const recentCandles = candlesForPeriod.slice(lookbackStart);

                // Get the current trail step size multiplier from config
                const trailStepSizeMultiplier = config.trailStepSizeMultiplier || 0.1;

                // Create a modified config with the current trail step size multiplier
                const modifiedConfig = {
                    ...config,
                    trailStepSizeMultiplier: trailStepSizeMultiplier
                };

                // Estimate spread and calculate adaptive slippage
                const estimatedSpread = spreadUtils.estimateSpread(recentCandles, modifiedConfig);
                const adaptiveSlippage = modifiedConfig.useAdaptiveSlippage ?
                    spreadUtils.calculateAdaptiveSlippage(recentCandles, modifiedConfig) :
                    modifiedConfig.slippagePoints || 0.5;

                exitInfo = enhancedPM.enhancedLogAndStoreExit(
                    pos, 'end_of_period', 0, exitP, exitTs, completedTrades, modifiedConfig, exitCounts,
                    adaptiveSlippage, estimatedSpread
                );
            } else {
                exitInfo = logAndStoreExit(pos, 'end_of_period', 0, exitP, exitTs, completedTrades, config, exitCounts);
            }

            balance += exitInfo.pnlNetTotal;
            const day = getDayIdentifier(exitTs.getTime()/1000);
            currentDailyPnLMap.set(day, (currentDailyPnLMap.get(day) || 0) + exitInfo.pnlNetTotal);

            // HOURLY TRACKING: Update hourly stats when trade exits
            const exitHour = exitTs.getUTCHours();
            hourlyStats[exitHour].totalTrades++;
            hourlyStats[exitHour].totalPnL += exitInfo.pnlNetTotal;
            if(exitInfo.pnlNetTotal > 0) {
                hourlyStats[exitHour].winningTrades++;
                hourlyStats[exitHour].grossProfit += exitInfo.pnlNetTotal;
            } else {
                hourlyStats[exitHour].losingTrades++;
                hourlyStats[exitHour].grossLoss += Math.abs(exitInfo.pnlNetTotal);
            }

            if(exitInfo.pnlNetTotal > 0) {
                wins++;
                grossProfit += exitInfo.pnlNetTotal;
            } else {
                losses++;
                grossLoss += Math.abs(exitInfo.pnlNetTotal);
            }

            peakBalanceWeekly = Math.max(peakBalanceWeekly, balance);
            maxDrawdownWeekly = Math.max(maxDrawdownWeekly, peakBalanceWeekly-balance);
            currentOverallPeak = Math.max(currentOverallPeak, balance);
            currentOverallMaxDD = Math.max(currentOverallMaxDD, currentOverallPeak-balance);
            pos = null;
        }
    } // End of main for loop
    const wkPnl=balance-initialBalance, winRate=(wins+losses)>0?(wins/(wins+losses)*100):0, pf=grossLoss>0?(grossProfit/grossLoss):(grossProfit>0?99999:0); const avgW=wins>0?(grossProfit/wins):0, avgL=losses>0?(grossLoss/losses):0;

    // HOURLY TRACKING: Calculate hourly win rates
    for (let hour = 0; hour < 24; hour++) {
        const stats = hourlyStats[hour];
        if (stats.totalTrades > 0) {
            stats.winRate = (stats.winningTrades / stats.totalTrades) * 100;
            stats.profitFactor = stats.grossLoss > 0 ? (stats.grossProfit / stats.grossLoss) : (stats.grossProfit > 0 ? 99999 : 0);
            stats.avgWin = stats.winningTrades > 0 ? (stats.grossProfit / stats.winningTrades) : 0;
            stats.avgLoss = stats.losingTrades > 0 ? (stats.grossLoss / stats.losingTrades) : 0;
        } else {
            stats.winRate = 0;
            stats.profitFactor = 0;
            stats.avgWin = 0;
            stats.avgLoss = 0;
        }
    }

    return { tradesTaken:trades, wins:wins, losses:losses, totalPnL:wkPnl, winRate:winRate, profitFactor:pf, avgWin:avgW, avgLoss:avgL, tradeLog:completedTrades, exitCounts:exitCounts, finalBalance:balance, peakBalance:currentOverallPeak, maxDrawdown:currentOverallMaxDD, maxDrawdownWeekly:`-${maxDrawdownWeekly.toFixed(config.pricePrecision)}`, dailyPnLMap:currentDailyPnLMap, hourlyStats:hourlyStats }; }

// --- 9) Position Management --- (Uses Exit@Close Latency Model)
function managePosition(currentPos, currentCandle, currentIndex, candlesForPeriod, completedTrades, config, exitCounts, maxTimeExitMinutes = null, entryParams = null) {
    const trailF = currentPos.trailFactor;
    const atrTrail = (typeof currentCandle.atr === 'number' && !isNaN(currentCandle.atr) && currentCandle.atr > 0) ? currentCandle.atr : currentPos.atr;

    // Only update trailing stop if not disabled
    if (!config.disableTrailingStop) {
        if (currentPos.dir === 'bullish') {
            currentPos.trailHigh = Math.max(currentPos.trailHigh, currentCandle.high);
            currentPos.trailStopPrice = Math.max(currentPos.trailStopPrice, currentPos.trailHigh-(atrTrail*trailF));
        } else {
            currentPos.trailLow = Math.min(currentPos.trailLow, currentCandle.low);
            currentPos.trailStopPrice = Math.min(currentPos.trailStopPrice, currentPos.trailLow+(atrTrail*trailF));
        }
    }

    let exitR = null, exitSigP = null;

    // Calculate bars in trade
    const barsInTrade = currentIndex - currentPos.entryBarIndex;

    // HYBRID 1-CANDLE EXIT STRATEGY: Exit after 1 candle ONLY if profitable
    const using1CandleExit = config.useImmediateExit && currentPos.immediateCandleExit === 1;

    // If using conditional 1-candle exit, only exit if trade is profitable
    if (using1CandleExit && barsInTrade >= 1 && config.useConditionalExit) {
        // Calculate current P&L in points
        const currentPnLPoints = currentPos.dir === 'bullish'
            ? currentCandle.close - currentPos.entry
            : currentPos.entry - currentCandle.close;

        // Only exit if trade is profitable (positive P&L)
        if (currentPnLPoints > 0) {
            exitR = 'time_1candle_profit';
            exitSigP = currentCandle.close;
        }
    }
    // Original 1-candle exit (exit regardless of profit/loss) - fallback
    else if (using1CandleExit && barsInTrade >= 1 && !config.useConditionalExit) {
        exitR = 'time_1candle';
        exitSigP = currentCandle.close;
    }

    // Tick-based intrabar exit resolution (decides TP vs SL order using ticks)
    if (config.intrabarResolutionMode === 'tick' && tickReplay && typeof tickReplay.resolveExitWithTicks === 'function') {
        const tickExit = tickReplay.resolveExitWithTicks(currentPos, candlesForPeriod, currentIndex, config);
        if (tickExit && tickExit.reason && tickExit.exitSignalPrice !== null && tickExit.exitSignalPrice !== undefined) {
            let finalExitP = tickExit.exitSignalPrice;
            let exitIdx = currentIndex;
            const lat = config.latencyDelayBars || 0;
            if (lat > 0 && (currentIndex + lat) < candlesForPeriod.length) {
                const idx = currentIndex + lat;
                const c = candlesForPeriod[idx];
                if (c && !isNaN(c.close)) { finalExitP = c.close; exitIdx = idx; }
            }
            const exitTs = (tickExit.exitTimestamp instanceof Date)
                ? tickExit.exitTimestamp
                : (typeof candlesForPeriod[exitIdx].timestamp === 'string'
                    ? new Date(candlesForPeriod[exitIdx].timestamp)
                    : new Date(candlesForPeriod[exitIdx].timestamp * 1000));
            currentPos.currentBarIndex = exitIdx;
            const exitInfo = logAndStoreExit(currentPos, tickExit.reason, 0, finalExitP, exitTs, completedTrades, config, exitCounts);
            return exitInfo;
        }
    }

    // Only check TP if NOT using 1-candle exit strategy
    if (!using1CandleExit && currentPos.tpDistance > 0) {
        if (currentPos.dir === 'bullish') {
            if (currentCandle.high >= currentPos.entry + currentPos.tpDistance) {
                exitR = 'tp';
                exitSigP = currentPos.entry + currentPos.tpDistance;
            }
        } else {
            if (currentCandle.low <= currentPos.entry - currentPos.tpDistance) {
                exitR = 'tp';
                exitSigP = currentPos.entry - currentPos.tpDistance;
            }
        }
    }

    // Check for Stop Loss (ADDED - was missing!)
    if (!exitR && !using1CandleExit && currentPos.slDistance > 0) {
        if (currentPos.dir === 'bullish') {
            if (currentCandle.low <= currentPos.stopLossPrice) {
                exitR = 'sl';
                exitSigP = currentPos.stopLossPrice;
            }
        } else {
            if (currentCandle.high >= currentPos.stopLossPrice) {
                exitR = 'sl';
                exitSigP = currentPos.stopLossPrice;
            }
        }
    }

    // IMPROVED EXIT STRATEGY: Only exit on opposing full entry criteria or max time
    if (!exitR && currentIndex >= 3) {
        const barsInTrade = currentIndex - currentPos.entryBarIndex;

        // Check for FULL opposing entry criteria (same as original entry requirements)
        const c1 = candlesForPeriod[currentIndex - 2];
        const c2 = candlesForPeriod[currentIndex - 1];
        const c3 = currentCandle;

        // Check for opposing 3-candle pattern
        const opposingPattern3 = detect3(c1, c2, c3);

        // Only exit if we get FULL opposing entry criteria
        if (opposingPattern3) {
            // Check if opposing pattern would trigger a full entry (with ALL filters)
            const wouldTriggerOpposingEntry = entryOK(opposingPattern3, 'three', c3, currentIndex, candlesForPeriod, entryParams);

            // Only exit if opposing signal is opposite to current position AND passes all entry filters
            if (wouldTriggerOpposingEntry &&
                ((currentPos.dir === 'bullish' && opposingPattern3 === 'bearish') ||
                 (currentPos.dir === 'bearish' && opposingPattern3 === 'bullish'))) {
                exitR = 'opposing_full_entry';
                exitSigP = currentCandle.close;
            }
        }

        // 200 WMA BREAK EXIT: Cut losses when price breaks to opposite side of 200 WMA
        if (!exitR && config.use200WmaBreakExit && !isNaN(currentCandle.wma200)) {
            if (currentPos.dir === 'bullish' && currentCandle.close < currentCandle.wma200) {
                // Long position: exit if price breaks below 200 WMA
                exitR = '200wma_break';
                exitSigP = currentCandle.close;
            } else if (currentPos.dir === 'bearish' && currentCandle.close > currentCandle.wma200) {
                // Short position: exit if price breaks above 200 WMA
                exitR = '200wma_break';
                exitSigP = currentCandle.close;
            }
        }

        // Backup exit: Maximum candle limit (only if no TP or opposing signal)
        // Use parameter-specific max time or fallback to config/default
        const maxTimeLimit = maxTimeExitMinutes || config.maxTimeExitMinutes || 240;
        if (!exitR && barsInTrade >= maxTimeLimit) {
            exitR = `max_time_${maxTimeLimit}min`;
            exitSigP = currentCandle.close;
        }
    }

    if (!exitR && config.useTwoBarColorExit && currentIndex > currentPos.entryBarIndex + 1) {
        const prevC = candlesForPeriod[currentIndex-1],
              prevCol = candlestickColor(prevC),
              curCol = candlestickColor(currentCandle);

        if (currentPos.dir === 'bullish' && prevCol === 'red' && curCol === 'red') {
            exitR = 'color_flow_2bar';
            exitSigP = currentCandle.close;
        } else if (currentPos.dir === 'bearish' && prevCol === 'green' && curCol === 'green') {
            exitR = 'color_flow_2bar';
            exitSigP = currentCandle.close;
        }
    }

    if (exitR && exitSigP !== null) {
        let finalExitP = exitSigP, exitIdx = currentIndex;
        const lat = config.latencyDelayBars || 0;

        if (lat > 0 && (currentIndex + lat) < candlesForPeriod.length) {
            const idx = currentIndex + lat, c = candlesForPeriod[idx];
            if (c && !isNaN(c.open) && !isNaN(c.close)) {
                finalExitP = c.close;
                exitIdx = idx;
            } else {
                finalExitP = currentCandle.close;
                exitIdx = currentIndex;
            }
        } else if (lat > 0) {
            finalExitP = candlesForPeriod[candlesForPeriod.length-1].close;
            exitIdx = candlesForPeriod.length-1;
        }

        const exitTs = typeof candlesForPeriod[exitIdx].timestamp === 'string' ?
            new Date(candlesForPeriod[exitIdx].timestamp) :
            new Date(candlesForPeriod[exitIdx].timestamp * 1000);
        currentPos.currentBarIndex = exitIdx;
        const exitInfo = logAndStoreExit(currentPos, exitR, 0, finalExitP, exitTs, completedTrades, config, exitCounts);
        return exitInfo;
    }

    return null;
}

// --- 10) Trade Logging ---
function logAndStoreExit(posData, reason, pnlPointsTheoretical, exitSignalPrice, exitTimestamp, completedTradesArray, config, exitCounts) {
    if (!posData || typeof posData.entry !== 'number' || !posData.dir || !exitTimestamp) {
        console.error("logAndStoreExit: Invalid data.");
        return { pnlNetTotal: 0, exitTimestamp: exitTimestamp || new Date() };
    }
    const contracts=posData.contracts||1, ptVal=config.pointValue;
    let adjExitP=exitSignalPrice;
    const slipPts=config.slippagePoints||0;
    const applySlip=(reason==='sl'||reason==='trail'||reason==='end_of_period'||reason==='hold_expired'||reason==='color_flow_2bar');
    if(applySlip&&slipPts>0){
        adjExitP=(posData.dir==='bullish')?exitSignalPrice-slipPts:exitSignalPrice+slipPts;
    }
    const pnlPtsAdj=(posData.dir==='bullish')?adjExitP-posData.entry:posData.entry-adjExitP;
    const pnlGross=pnlPtsAdj*ptVal*contracts, commCost=config.commissionPerContract*contracts, pnlNet=pnlGross-commCost;
    const durBars=(typeof posData.currentBarIndex==='number'&&typeof posData.entryBarIndex==='number'&&posData.currentBarIndex>=posData.entryBarIndex)?posData.currentBarIndex-posData.entryBarIndex+1:'N/A';
    if(exitCounts){
        exitCounts[reason]=(exitCounts[reason]||0)+1;
    }

    // Handle timestamp conversion
    const exitTimeStr = exitTimestamp.toISOString();
    const entryTimeStr = posData.entryTimestamp?.toISOString() || 'N/A';

    const tradeData={
        ID:posData.tradeId||'N/A',
        EntryTime:entryTimeStr,
        ExitTime:exitTimeStr,
        Dir:posData.dir,
        Entry:posData.entry.toFixed(config.pricePrecision),
        ExitSignal:exitSignalPrice.toFixed(config.pricePrecision),
        ExitFill:adjExitP.toFixed(config.pricePrecision),
        Reason:reason,
        PnL_Pts_Gross:pnlPtsAdj.toFixed(config.pricePrecision),
        PnL_Net:pnlNet.toFixed(2),
        SlippagePts:applySlip?slipPts:0,
        CommissionCost:commCost.toFixed(2),
        Contracts:contracts,
        Duration:durBars,
        EntryATRRegime:posData.entryAtrRegime||'N/A',
        TP_Type:posData.tpType||'N/A'
    };
    completedTradesArray.push(tradeData);
    return { pnlNetTotal:pnlNet, exitTimestamp:exitTimestamp };
}

// --- 11) Param Combinations --- (Corrected Version)
function getParameterCombinations() {
    const combos = [];

    // Check if we're using fixed points for SL/TP
    if (config.useFixedPointsForSLTP && Array.isArray(config.fixedSlPointsGrid) && Array.isArray(config.fixedTpPointsGrid)) {
        console.log(" -> Using fixed points grid for SL/TP");
        const slPointsArray = config.fixedSlPointsGrid;
        const tpPointsArray = config.fixedTpPointsGrid;
        const trailArray = Array.isArray(config.trailFactors) ? config.trailFactors : [config.trailFactors];
        const trailStepArray = Array.isArray(config.trailStepSizeMultiplier) ? config.trailStepSizeMultiplier : [config.trailStepSizeMultiplier];

        let isValid = true;
        if (!slPointsArray || slPointsArray.length === 0) { console.error("Error: Invalid 'fixedSlPointsGrid'."); isValid = false; }
        if (!tpPointsArray || tpPointsArray.length === 0) { console.error("Error: Invalid 'fixedTpPointsGrid'."); isValid = false; }

        if (isValid) {
            // Get immediate candle exit options if enabled
            const immediateCandleArray = config.useImmediateExit && Array.isArray(config.immediateCandleExitGrid)
                ? config.immediateCandleExitGrid
                : [null];

            // Use single max hold time value
            const maxHoldTimeArray = [config.maxTimeExitMinutes || 240];

            for (let slPoints of slPointsArray) {
                for (let tpPoints of tpPointsArray) {
                    for (let trail of trailArray) {
                        for (let trailStep of trailStepArray) {
                            for (let immediateCandleExit of immediateCandleArray) {
                                for (let maxHoldTime of maxHoldTimeArray) {
                                    if (typeof slPoints === 'number' && !isNaN(slPoints) &&
                                        typeof tpPoints === 'number' && !isNaN(tpPoints) &&
                                        typeof trail === 'number' && !isNaN(trail) &&
                                        typeof trailStep === 'number' && !isNaN(trailStep) &&
                                        typeof maxHoldTime === 'number' && !isNaN(maxHoldTime)) {
                                        combos.push({
                                            slFactor: 0, // Not using ATR-based SL
                                            tpFactor: 0, // Not using ATR-based TP
                                            trailFactor: trail,
                                            trailStepSizeMultiplier: trailStep,
                                            fixedSlPoints: slPoints,
                                            fixedTpPoints: tpPoints,
                                            useFixedPoints: true,
                                            immediateCandleExit: immediateCandleExit,
                                            maxTimeExitMinutes: maxHoldTime
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    } else {
        // ATR-based parameter combinations with ATR range testing
        const slArray = Array.isArray(config.slFactors) ? config.slFactors : [config.slFactors];
        const tpArray = Array.isArray(config.tpFactors) ? config.tpFactors : [config.tpFactors];
        const trailArray = Array.isArray(config.trailFactors) ? config.trailFactors : [config.trailFactors];
        const trailStepArray = Array.isArray(config.trailStepSizeMultiplier) ? config.trailStepSizeMultiplier : [config.trailStepSizeMultiplier];

        // ATR range testing arrays (disabled - using fixed points)
        const minAtrArray = [config.minAtrEntry || 0];
        const maxAtrArray = [config.maxAtrEntry || 999];

        let isValid = true;
        if (!slArray || slArray.length === 0 || typeof slArray[0] !== 'number' || isNaN(slArray[0])) { console.error("Error: Invalid 'slFactors'."); isValid = false; }
        if (!tpArray || tpArray.length === 0 || typeof tpArray[0] !== 'number' || isNaN(tpArray[0])) { console.error("Error: Invalid 'tpFactors'."); isValid = false; }
        if (!trailArray || trailArray.length === 0 || typeof trailArray[0] !== 'number' || isNaN(trailArray[0])) { console.error("Error: Invalid 'trailFactors'."); isValid = false; }

        if (isValid) {
            for (let sl of slArray) {
                for (let tp of tpArray) {
                    for (let trail of trailArray) {
                        for (let trailStep of trailStepArray) {
                            for (let minAtr of minAtrArray) {
                                for (let maxAtr of maxAtrArray) {
                                    if (typeof sl === 'number' && !isNaN(sl) &&
                                        typeof tp === 'number' && !isNaN(tp) &&
                                        typeof trail === 'number' && !isNaN(trail) &&
                                        typeof trailStep === 'number' && !isNaN(trailStep) &&
                                        typeof minAtr === 'number' && !isNaN(minAtr) &&
                                        typeof maxAtr === 'number' && !isNaN(maxAtr) &&
                                        minAtr <= maxAtr) {
                                        combos.push({
                                            slFactor: sl,
                                            tpFactor: tp,
                                            trailFactor: trail,
                                            trailStepSizeMultiplier: trailStep,
                                            useFixedPoints: false,
                                            minAtrEntry: minAtr,
                                            maxAtrEntry: maxAtr
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    return combos; // Always return the array
}

// --- 12) Metrics --- (moved to top of file)

// --- 13) Results Aggregation --- (moved to top of file)

// --- 14) Results Display --- (moved to top of file)

// --- 15) Save Trade Log --- (moved to top of file)

// --- DATA LOADING FUNCTION ---
function loadCandleData() {
    console.log("📊 Loading candle data...");

    const inputFile = config.inputFile;
    if (!fs.existsSync(inputFile)) {
        console.error(`❌ Input file not found: ${inputFile}`);
        return null;
    }

    console.log(`📂 Reading file: ${inputFile}`);

    const data = [];
    const csvContent = fs.readFileSync(inputFile, 'utf8');
    const lines = csvContent.split('\n');
    const header = (lines[0] || '').trim();
    const isDatabento = header.includes('ts_event');

    // Skip header line
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split(',');
        try {
            if (isDatabento) {
                // Databento OHLCV-1m: ts_event,rtype,publisher_id,instrument_id,open,high,low,close,volume,symbol
                if (parts.length < 10) continue;
                const tsStr = parts[0];
                const open = parseFloat(parts[4]);
                const high = parseFloat(parts[5]);
                const low = parseFloat(parts[6]);
                const close = parseFloat(parts[7]);
                const volume = parseInt(parts[8]) || 0;
                if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) continue;

                const tsNum = Number(tsStr);
                let tsSeconds;
                if (tsNum > 1e15) { // nanoseconds
                    tsSeconds = Math.floor(tsNum / 1e9);
                } else if (tsNum > 1e11) { // milliseconds
                    tsSeconds = Math.floor(tsNum / 1e3);
                } else {
                    tsSeconds = tsNum; // seconds
                }
                const date = new Date(tsSeconds * 1000);
                if (isNaN(date.getTime())) continue;

                data.push({
                    timestamp: date.toISOString(),
                    open, high, low, close, volume
                });
            } else {
                // Standard format: Time,Open,High,Low,Close,Volume,Symbol
                if (parts.length < 6) continue;
                const timeStr = parts[0];
                const open = parseFloat(parts[1]);
                const high = parseFloat(parts[2]);
                const low = parseFloat(parts[3]);
                const close = parseFloat(parts[4]);
                const volume = parseInt(parts[5]) || 0;
                if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) continue;

                let date;
                if (/^\d+$/.test(timeStr)) {
                    // numeric epoch (assume seconds)
                    date = new Date(parseInt(timeStr, 10) * 1000);
                } else {
                    date = new Date(timeStr);
                }
                if (isNaN(date.getTime())) continue;

                data.push({
                    timestamp: date.toISOString(),
                    open, high, low, close, volume
                });
            }
        } catch (_) {
            continue;
        }
    }

    console.log(`✅ Loaded ${data.length.toLocaleString()} candles`);

    // Apply front month filtering if configured
    if (config.frontMonthStart && config.frontMonthEnd) {
        const startDate = new Date(config.frontMonthStart + 'T00:00:00Z');
        const endDate = new Date(config.frontMonthEnd + 'T23:59:59Z');

        const filteredData = data.filter(candle => {
            const ts = candle.timestamp;
            let candleDate;
            if (typeof ts === 'number') {
                // Handle seconds vs milliseconds
                candleDate = new Date(ts < 1e12 ? ts * 1000 : ts);
            } else {
                candleDate = new Date(ts);
            }
            return candleDate >= startDate && candleDate <= endDate;
        });

        console.log(`📅 Applied front month filter (${config.frontMonthStart} to ${config.frontMonthEnd})`);
        console.log(`📊 Filtered data: ${filteredData.length.toLocaleString()} candles`);
        if (filteredData.length === 0 && data.length > 0) {
            const sample = data.slice(0, Math.min(3, data.length)).map(c => c.timestamp);
            console.warn('⚠️ Filter produced 0 candles; example timestamps:', sample);
            console.warn('StartDate UTC:', startDate.toISOString(), 'EndDate UTC:', endDate.toISOString());
        }

        return filteredData;
    }

    return data;
}

// --- TOP 20 COMBINATIONS ANALYSIS ---
function runTop20Analysis(candleData) {
    console.log("🏆 RUNNING TOP 20 SL/TP COMBINATIONS ANALYSIS...\n");

    // Top 20 combinations based on our previous 900-test results
    const top20Combinations = [
        {sl: 1, tp: 1, label: "SL=1,TP=1 (Ultimate Scalping)"},
        {sl: 2, tp: 1, label: "SL=2,TP=1 (Aggressive Scalping)"},
        {sl: 3, tp: 1, label: "SL=3,TP=1 (Balanced Scalping)"},
        {sl: 1, tp: 2, label: "SL=1,TP=2 (Micro Scalping)"},
        {sl: 4, tp: 1, label: "SL=4,TP=1 (High Win Rate)"},
        {sl: 2, tp: 2, label: "SL=2,TP=2 (Conservative)"},
        {sl: 5, tp: 1, label: "SL=5,TP=1 (Extended)"},
        {sl: 3, tp: 2, label: "SL=3,TP=2 (Balanced)"},
        {sl: 6, tp: 1, label: "SL=6,TP=1 (Wide Stop)"},
        {sl: 1, tp: 3, label: "SL=1,TP=3 (Tight/Wide)"},
        {sl: 4, tp: 2, label: "SL=4,TP=2 (Moderate)"},
        {sl: 7, tp: 1, label: "SL=7,TP=1 (Very Wide)"},
        {sl: 2, tp: 3, label: "SL=2,TP=3 (Mixed)"},
        {sl: 5, tp: 2, label: "SL=5,TP=2 (Extended Balanced)"},
        {sl: 8, tp: 1, label: "SL=8,TP=1 (Ultra Wide)"},
        {sl: 3, tp: 3, label: "SL=3,TP=3 (Equal Ratio)"},
        {sl: 1, tp: 4, label: "SL=1,TP=4 (High Reward)"},
        {sl: 6, tp: 2, label: "SL=6,TP=2 (Wide/Moderate)"},
        {sl: 9, tp: 1, label: "SL=9,TP=1 (Maximum Wide)"},
        {sl: 4, tp: 3, label: "SL=4,TP=3 (Balanced Plus)"}
    ];

    const allResults = [];
    const aggregatedHourlyStats = {};

    // Initialize aggregated hourly stats
    for (let hour = 0; hour < 24; hour++) {
        aggregatedHourlyStats[hour] = {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            totalPnL: 0,
            grossProfit: 0,
            grossLoss: 0,
            combinations: []
        };
    }

    console.log("🔄 Testing top 20 combinations...\n");

    for (let i = 0; i < top20Combinations.length; i++) {
        const combo = top20Combinations[i];
        console.log(`\n📊 Testing ${i+1}/20: ${combo.label}`);

        // Run backtest for this combination
        const result = backtest(candleData, {
            initialBalance: config.initialBalance,
            overallPeakBalance: config.initialBalance,
            overallMaxDrawdown: 0,
            slFactor: 0,
            tpFactor: 0,
            trailFactor: 0,
            isAdaptiveRun: false,
            dailyPnLMap: new Map(),
            fixedSlPoints: combo.sl,
            fixedTpPoints: combo.tp,
            useFixedPoints: true,
            immediateCandleExit: false,
            maxTimeExitMinutes: null,
            minAtrEntry: 0,
            maxAtrEntry: 999
        });

        // Store result with combination info
        const combinationResult = {
            ...result,
            slPoints: combo.sl,
            tpPoints: combo.tp,
            label: combo.label,
            rank: i + 1
        };

        allResults.push(combinationResult);

        // Aggregate hourly stats
        if (result.hourlyStats) {
            for (let hour = 0; hour < 24; hour++) {
                const hourData = result.hourlyStats[hour];
                if (hourData && hourData.totalTrades > 0) {
                    aggregatedHourlyStats[hour].totalTrades += hourData.totalTrades;
                    aggregatedHourlyStats[hour].winningTrades += hourData.winningTrades;
                    aggregatedHourlyStats[hour].losingTrades += hourData.losingTrades;
                    aggregatedHourlyStats[hour].totalPnL += hourData.totalPnL;
                    aggregatedHourlyStats[hour].grossProfit += hourData.grossProfit;
                    aggregatedHourlyStats[hour].grossLoss += hourData.grossLoss;
                    aggregatedHourlyStats[hour].combinations.push({
                        combo: combo.label,
                        trades: hourData.totalTrades,
                        pnl: hourData.totalPnL,
                        winRate: hourData.winRate
                    });
                }
            }
        }

        console.log(`✅ ${combo.label}: $${result.totalPnL.toFixed(0)} | ${result.winRate.toFixed(1)}% | ${result.tradesTaken} trades`);
    }

    // Calculate final aggregated hourly metrics
    for (let hour = 0; hour < 24; hour++) {
        const stats = aggregatedHourlyStats[hour];
        if (stats.totalTrades > 0) {
            stats.winRate = (stats.winningTrades / stats.totalTrades) * 100;
            stats.profitFactor = stats.grossLoss > 0 ? (stats.grossProfit / stats.grossLoss) : (stats.grossProfit > 0 ? 99999 : 0);
            stats.avgWin = stats.winningTrades > 0 ? (stats.grossProfit / stats.winningTrades) : 0;
            stats.avgLoss = stats.losingTrades > 0 ? (stats.grossLoss / stats.losingTrades) : 0;
            stats.avgPnLPerTrade = stats.totalPnL / stats.totalTrades;
        }
    }

    // Display results
    displayTop20Results(allResults, aggregatedHourlyStats);

    return { allResults, aggregatedHourlyStats };
}

function displayTop20Results(allResults, aggregatedHourlyStats) {
    console.log("\n\n🏆 ===== TOP 20 COMBINATIONS RESULTS ===== 🏆\n");

    // Sort by total PnL
    const sortedResults = allResults.sort((a, b) => b.totalPnL - a.totalPnL);

    console.log("Rank | SL/TP | Total P&L | Win Rate | Trades | PF | Sharpe | Label");
    console.log("-".repeat(85));

    for (let i = 0; i < sortedResults.length; i++) {
        const r = sortedResults[i];
        const sharpe = calculateSharpe(r.totalPnL, r.tradesTaken);
        console.log(`${(i+1).toString().padStart(4)} | ${r.slPoints}/${r.tpPoints} | ${r.totalPnL.toFixed(0).padStart(9)} | ${r.winRate.toFixed(1).padStart(7)}% | ${r.tradesTaken.toString().padStart(6)} | ${r.profitFactor.toFixed(2).padStart(4)} | ${sharpe.toFixed(2).padStart(6)} | ${r.label}`);
    }

    console.log("\n\n🕐 ===== AGGREGATED HOURLY ANALYSIS (TOP 20 COMBINATIONS) ===== 🕐\n");
    console.log("Hour | Trades | Win% | Net P&L | Avg P&L/Trade | PF | Combinations");
    console.log("-".repeat(90));

    for (let hour = 0; hour < 24; hour++) {
        const h = aggregatedHourlyStats[hour];
        const hourStr = hour.toString().padStart(2, '0') + ':00 UTC';
        if (h.totalTrades > 0) {
            console.log(`${hourStr} | ${h.totalTrades.toString().padStart(6)} | ${h.winRate.toFixed(1).padStart(4)}% | ${h.totalPnL.toFixed(0).padStart(7)} | ${h.avgPnLPerTrade.toFixed(1).padStart(11)} | ${h.profitFactor === 99999 ? 'Inf' : h.profitFactor.toFixed(2).padStart(4)} | ${h.combinations.length} combos`);
        } else {
            console.log(`${hourStr} | ${h.totalTrades.toString().padStart(6)} | ${'N/A'.padStart(4)} | ${'0'.padStart(7)} | ${'N/A'.padStart(11)} | ${'N/A'.padStart(4)} | 0 combos`);
        }
    }

    // Best and worst hours
    const hoursWithTrades = [];
    for (let hour = 0; hour < 24; hour++) {
        if (aggregatedHourlyStats[hour].totalTrades >= 10) { // Minimum 10 trades for significance
            hoursWithTrades.push({
                hour: hour,
                ...aggregatedHourlyStats[hour]
            });
        }
    }

    hoursWithTrades.sort((a, b) => b.totalPnL - a.totalPnL);

    console.log("\n🏆 BEST PERFORMING HOURS (Min 10 trades):");
    console.log("Hour | Net P&L | Win Rate | Trades | Avg P&L/Trade");
    console.log("-".repeat(55));
    for (let i = 0; i < Math.min(5, hoursWithTrades.length); i++) {
        const h = hoursWithTrades[i];
        const hourStr = h.hour.toString().padStart(2, '0') + ':00 UTC';
        console.log(`${hourStr} | ${h.totalPnL.toFixed(0).padStart(7)} | ${h.winRate.toFixed(1).padStart(7)}% | ${h.totalTrades.toString().padStart(6)} | ${h.avgPnLPerTrade.toFixed(1).padStart(11)}`);
    }

    console.log("\n🚨 WORST PERFORMING HOURS (Min 10 trades):");
    console.log("Hour | Net P&L | Win Rate | Trades | Avg P&L/Trade");
    console.log("-".repeat(55));
    const worstHours = hoursWithTrades.slice(-5).reverse();
    for (let i = 0; i < worstHours.length; i++) {
        const h = worstHours[i];
        const hourStr = h.hour.toString().padStart(2, '0') + ':00 UTC';
        console.log(`${hourStr} | ${h.totalPnL.toFixed(0).padStart(7)} | ${h.winRate.toFixed(1).padStart(7)}% | ${h.totalTrades.toString().padStart(6)} | ${h.avgPnLPerTrade.toFixed(1).padStart(11)}`);
    }

    // Save detailed results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputDir = path.join(__dirname, 'output', 'MNQ_LastYear_TradeLog');

    // Save aggregated hourly data
    const hourlyFile = path.join(outputDir, `top20_hourly_analysis_${timestamp}.json`);
    fs.writeFileSync(hourlyFile, JSON.stringify(aggregatedHourlyStats, null, 2));
    console.log(`\n💾 Hourly analysis saved to: ${hourlyFile}`);

    // Save all combination results
    const resultsFile = path.join(outputDir, `top20_combinations_results_${timestamp}.json`);
    fs.writeFileSync(resultsFile, JSON.stringify(allResults, null, 2));
    console.log(`💾 Combination results saved to: ${resultsFile}`);
}

function calculateSharpe(totalPnL, trades) {
    if (trades === 0) return 0;
    const avgReturn = totalPnL / trades;
    // Simplified Sharpe calculation
    return avgReturn * Math.sqrt(trades) / 100;
}

// --- COMPREHENSIVE HOURLY ANALYSIS FUNCTION ---
function generateComprehensiveHourlyAnalysis(allResults) {
    console.log("📊 Generating comprehensive hourly analysis for all SL/TP combinations...");

    // Extract top performers by different metrics
    const sortedByProfit = [...allResults].sort((a, b) => parseFloat(b.TotalPnL) - parseFloat(a.TotalPnL));
    const sortedByWinRate = [...allResults].sort((a, b) => parseFloat(b.WinRate) - parseFloat(a.WinRate));

    const topN = config.topNResults || 100; // expanded deliverables
    const topProfit = sortedByProfit.slice(0, topN);
    const topWinRate = sortedByWinRate.slice(0, topN);

    console.log(`\n🏆 TOP ${topN} COMBINATIONS BY TOTAL PROFIT:`);
    console.log("Rank | SL | TP | Total P&L | Win Rate | Profit Factor | Trades | $/Week");
    console.log("-----|----|----|-----------|----------|---------------|--------|--------");

    topProfit.forEach((result, index) => {
        const weeklyProfit = (parseFloat(result.TotalPnL) / result.Weeks).toFixed(0);
        console.log(`${(index + 1).toString().padStart(4)} | ${result.SL_Run.toString().padStart(2)} | ${result.TP_Run.toString().padStart(2)} | $${parseFloat(result.TotalPnL).toFixed(0).padStart(8)} | ${result.WinRate.padStart(7)} | ${result.ProfitFactor.toString().padStart(12)} | ${result.Trades.toString().padStart(6)} | $${weeklyProfit.padStart(6)}`);
    });

    console.log(`\n🎯 TOP ${topN} COMBINATIONS BY WIN RATE:`);
    console.log("Rank | SL | TP | Win Rate | Total P&L | Profit Factor | Trades | $/Week");
    console.log("-----|----|----|----------|-----------|---------------|--------|--------");

    topWinRate.forEach((result, index) => {
        const weeklyProfit = (parseFloat(result.TotalPnL) / result.Weeks).toFixed(0);
        console.log(`${(index + 1).toString().padStart(4)} | ${result.SL_Run.toString().padStart(2)} | ${result.TP_Run.toString().padStart(2)} | ${result.WinRate.padStart(7)} | $${parseFloat(result.TotalPnL).toFixed(0).padStart(8)} | ${result.ProfitFactor.toString().padStart(12)} | ${result.Trades.toString().padStart(6)} | $${weeklyProfit.padStart(6)}`);
    });

    // Create detailed hourly breakdown matrix
    const hourlyMatrix = generateDetailedHourlyMatrix(allResults);

    // Save expanded deliverables
    const deliverable = {
        run: new Date().toISOString(),
        topN,
        topByProfit: topProfit,
        topByWinRate: topWinRate,
        allResults,
        hourlyMatrix
    };
    try {
        const baseName = `grid_results_${new Date().toISOString().replace(/[:.]/g,'-')}`;
        fs.writeFileSync(path.join(deliverablesDir, `${baseName}.json`), JSON.stringify(deliverable, null, 2));
        const toCsv = (rows) => {
            if (!rows || rows.length===0) return '';
            const headers = Object.keys(rows[0]);
            const lines = [headers.join(',')].concat(rows.map(r => headers.map(h => r[h]).join(',')));
            return lines.join('\n');
        };
        fs.writeFileSync(path.join(deliverablesDir, `${baseName}_top_by_profit.csv`), toCsv(topProfit));
        fs.writeFileSync(path.join(deliverablesDir, `${baseName}_top_by_winrate.csv`), toCsv(topWinRate));
        fs.writeFileSync(path.join(deliverablesDir, `${baseName}_all_results.csv`), toCsv(allResults));
        console.log(`\n💾 Expanded deliverables saved under ${deliverablesDir}`);
    } catch (e) {
        console.warn('WARN: Failed to save expanded deliverables:', e?.message);
    }

    return {
        topProfit,
        topWinRate,
        hourlyMatrix,
        generatedAt: new Date().toISOString()
    };
}

// --- DETAILED HOURLY MATRIX FUNCTION ---
function generateDetailedHourlyMatrix(allResults) {
    console.log("\n📊 Generating detailed hourly P&L matrix for all SL/TP combinations...");

    // Create matrix structure: [SL][TP][Hour] = P&L
    const hourlyMatrix = {};

    // Initialize matrix structure
    for (let sl = 1; sl <= 10; sl++) {
        hourlyMatrix[sl] = {};
        for (let tp = 1; tp <= 10; tp++) {
            hourlyMatrix[sl][tp] = {};
            for (let hour = 0; hour < 24; hour++) {
                hourlyMatrix[sl][tp][hour] = {
                    pnl: 0,
                    trades: 0,
                    wins: 0,
                    losses: 0,
                    winRate: 0
                };
            }
        }
    }

    // Note: Since we don't have individual trade timestamps in the current aggregated results,
    // we'll use the overall hourly analysis data and distribute it proportionally
    // This is a limitation of the current data structure

    console.log("✅ Hourly matrix structure created for 100 SL/TP combinations");
    console.log("📝 Note: Detailed hourly trade data requires individual trade logs with timestamps");

    return {
        structure: "10x10 SL/TP matrix with 24-hour breakdown",
        combinations: Object.keys(hourlyMatrix).length * Object.keys(hourlyMatrix[1]).length,
        note: "Individual trade timestamps needed for precise hourly P&L distribution",
        matrixData: hourlyMatrix
    };
}

// --- MAIN EXECUTION ---
if (require.main === module) {
    console.log("🚀 Starting MNQ Backtest Engine...\n");

    // Load candle data
    const candleData = loadCandleData();
    if (!candleData || candleData.length === 0) {
        console.error("❌ No candle data loaded. Exiting.");
        process.exit(1);
    }

    console.log(`📊 Loaded ${candleData.length.toLocaleString()} candles`);
    console.log(`📅 Date range: ${candleData[0].timestamp} to ${candleData[candleData.length-1].timestamp}`);
    console.log(`⏰ Timeframe: ${config.timeframe}\n`);

    // Check if we should run top 20 analysis
    const args = process.argv.slice(2);
    if (args.includes('--top20')) {
        runTop20Analysis(candleData);
    } else {
        // Run the regular backtest
        global.allCandles = candleData;
        runWeeklyBacktests();
    }
}

// ======================== End of file ========================