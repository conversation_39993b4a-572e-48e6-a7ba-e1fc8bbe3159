const fs = require('fs');
const path = require('path');

const OUT = process.argv[2] || 'D:/backtest-output/tick-grid-front-month-MNQU4';
const summariesDir = path.join(OUT, 'summaries');
if (!fs.existsSync(summariesDir)) { console.error('No summaries dir:', summariesDir); process.exit(2); }

const files = fs.readdirSync(summariesDir).filter(f => /^SL\d+_TP\d+\.json$/.test(f));
let pos = [], neg = [], zero = [];
for (const f of files){
  try {
    const j = JSON.parse(fs.readFileSync(path.join(summariesDir,f),'utf8'));
    if (typeof j.profit !== 'number') continue;
    const rec = { name: f, profit: j.profit, trades: j.totalTrades||0, wr: j.winRate||0 };
    if (j.profit > 0) pos.push(rec); else if (j.profit < 0) neg.push(rec); else zero.push(rec);
  } catch {}
}
pos.sort((a,b)=>b.profit-a.profit);
neg.sort((a,b)=>a.profit-b.profit);

console.log(JSON.stringify({
  outDir: OUT,
  completed: files.length,
  positives: pos.length,
  negatives: neg.length,
  zeros: zero.length,
  topPos: pos.slice(0,5),
  topNeg: neg.slice(0,5)
}, null, 2));

