const fs = require('fs');
const path = require('path');

// Usage: node compare_sl4_tp20.js <per_day_json> <five_day_summary_json>
const perDayPath = process.argv[2];
const summaryPath = process.argv[3];
if (!perDayPath || !summaryPath){
  console.error('Usage: node compare_sl4_tp20.js <per_day_json> <five_day_summary_json>');
  process.exit(1);
}

function safeReadJSON(p){ try { return JSON.parse(fs.readFileSync(p,'utf8')); } catch(e){ return null; } }

const perDay = safeReadJSON(perDayPath);
const summary = safeReadJSON(summaryPath);
if (!Array.isArray(perDay)){
  console.error('Per-day JSON not an array or missing:', perDayPath);
  process.exit(2);
}
if (!summary){
  console.error('Summary JSON missing or unreadable:', summaryPath);
  process.exit(3);
}

const top = (Array.isArray(summary.topByProfit) ? summary.topByProfit : []).find(x=>x.sl===4 && x.tp===20);
const out = {
  perDayPath,
  summaryPath,
  perDayLen: perDay.length,
  perDayDates: perDay.map(d=>d.date),
  perDayTotalProfit: perDay.reduce((s,d)=>s+(d.profit||0),0),
  summary_days: summary.days,
  summary_combos: summary.combos,
  summary_top_sl4_tp20: top ? { profit: top.profit, totalTrades: top.totalTrades, winRate: top.winRate, perDay: top.perDay } : null
};

console.log(JSON.stringify(out,null,2));

