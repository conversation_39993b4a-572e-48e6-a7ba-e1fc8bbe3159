const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { spawnSync } = require('child_process');

// Helper to read CLI args like --start-date=YYYY-MM-DD
function getArg(name){
  const pref = name + '=';
  const hit = process.argv.find(a=>a.startsWith(pref));
  return hit ? hit.slice(pref.length) : null;
}

// Tick-validated front-month grid test
// - Entry signals from 1m OHLCV built from ticks
// - Exits validated on MBP-1 ticks
// - CME equity rollover: Monday prior to the 3rd Friday
// - Conservative fill model with 0.5 point slippage applied on exits

const CONFIG = {
  // Use ALL-HOURS OHLCV so indicators (e.g., WMA200) warm up before the 7–11 window
  ohlcvDir: process.env.OHLCV_DIR || 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/ohlcv',
  tickDir: process.env.TICK_DIR || 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/decompressed',
  parquetDir: process.env.PARQUET_DIR || 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/parquet_optimized',
  outDir: process.env.OUT_DIR || 'D:/backtest-output/tick-grid-front-month',

  // Grid (env overrides supported: SL_RANGE='1-3' or '1,2,3'; TP_RANGE similar)
  slRange: (process.env.SL_RANGE ? (process.env.SL_RANGE.includes('-') ? (function(){ const [a,b]=process.env.SL_RANGE.split('-').map(Number); const arr=[]; for(let i=a;i<=b;i++)arr.push(i); return arr; })() : process.env.SL_RANGE.split(',').map(Number)) : [1, 2, 3, 4, 5, 6]),
  tpRange: (process.env.TP_RANGE ? (process.env.TP_RANGE.includes('-') ? (function(){ const [a,b]=process.env.TP_RANGE.split('-').map(Number); const arr=[]; for(let i=a;i<=b;i++)arr.push(i); return arr; })() : process.env.TP_RANGE.split(',').map(Number)) : [4, 5, 6, 7, 8, 9, 10, 12, 14, 16, 18, 20]),
  tradingHoursUTCStandard: [13, 14, 15, 16, 17], // 7–11am CST
  tradingHoursUTCDST: [12, 13, 14, 15, 16],      // 7–11am CDT
  positionSize: 3,
  dollarsPerPoint: parseFloat(process.env.DOLLARS_PER_POINT || '2.0'), // MNQ micro = $2/point
  slippagePointsEntry: 0.25, // 1 tick at entry (~0.25)
  slippagePointsSL: 0.5,     // apply on stop loss only
  slippagePointsTP: 0.0,     // no slippage on TP (limit)
  topN: 200
};

function ensureDirs() {
  const subdirs = [
    CONFIG.outDir,
    path.join(CONFIG.outDir, 'logs'),
    path.join(CONFIG.outDir, 'signals'),
    path.join(CONFIG.outDir, 'trades'),
    path.join(CONFIG.outDir, 'summaries'),
    path.join(CONFIG.outDir, 'progress'),
    path.join(CONFIG.outDir, 'tmp')
  ];
function logJSON(relPath, obj){
  try { fs.writeFileSync(path.join(CONFIG.outDir, relPath), JSON.stringify(obj, null, 2)); } catch {}
}

  subdirs.forEach(d => { if (!fs.existsSync(d)) fs.mkdirSync(d, { recursive: true }); });
}

function quarterCode(monthIdx) { return ({2:'H',5:'M',8:'U',11:'Z'})[monthIdx]; }
function quarterMonths() { return [2,5,8,11]; }

function thirdFriday(year, monthIdx) {
  // monthIdx: 0-11; find 3rd Friday
  const d = new Date(Date.UTC(year, monthIdx, 1));
  // Move to first Friday
  while (d.getUTCDay() !== 5) d.setUTCDate(d.getUTCDate() + 1);
  // Add 14 days to reach third Friday
  d.setUTCDate(d.getUTCDate() + 14);
  return d;
}

function mondayPrior(dateObj) {
  // Return Monday before given date (not including the date itself)
  const d = new Date(dateObj.getTime());
  // Walk back to Monday
  while (d.getUTCDay() !== 1) d.setUTCDate(d.getUTCDate() - 1);
  return d;
}

function resolveFrontMonthSymbol(date) {
  // date: Date (UTC)
  // Find first quarter whose rollover Monday is AFTER this date; that quarter is current front month
  const y0 = date.getUTCFullYear() - 1;
  const candidates = [];
  for (let y = y0; y <= y0 + 3; y++) {
    for (const m of quarterMonths()) {
      const tf = thirdFriday(y, m); const roll = mondayPrior(tf);
      candidates.push({ y, m, roll });
    }
  }
  candidates.sort((a,b)=>a.roll - b.roll);
  const target = candidates.find(c => date < c.roll) || candidates[candidates.length-1];
  const y = target.y; const m = target.m;
  const code = quarterCode(m);
  const yy = String(y).slice(-1); // matches files like MNQH5
  return `MNQ${code}${yy}`;
}

function listUniqueDatesFromOHLCV() {
  const files = fs.readdirSync(CONFIG.ohlcvDir).filter(f => f.endsWith('.csv'));
  const dates = new Set();
  for (const f of files) {
    // Pattern 1: glbx-mdp3-YYYYMMDD.ohlcv-1m.SYMBOL.csv
    let m = f.match(/^glbx-mdp3-(\d{8})\.ohlcv-1m\.[A-Z0-9]+\.csv$/);
    if (!m){
      // Pattern 2: ohlcv_YYYYMMDD_SYMBOL.csv
      m = f.match(/^ohlcv_(\d{8})_[A-Z0-9]+\.csv$/);
    }
    if (m) dates.add(m[1]);
  }
  return Array.from(dates).sort();
}

function getTradingHoursForDate(dateStr){
  const y = Number(dateStr.slice(0,4));
  const m = Number(dateStr.slice(4,6));
  // Simple DST heuristic: Mar (3) through Oct (10) = DST
  const isDst = m >= 3 && m <= 10;
  return isDst ? CONFIG.tradingHoursUTCDST : CONFIG.tradingHoursUTCStandard;
}

async function loadOhlcvDay(dateStr, symbol) {
  // Load ALL hours for the day so indicators (WMA200/RSI) are fully warmed
  const file1 = path.join(CONFIG.ohlcvDir, `glbx-mdp3-${dateStr}.ohlcv-1m.${symbol}.csv`);
  const file2 = path.join(CONFIG.ohlcvDir, `ohlcv_${dateStr}_${symbol}.csv`); // fallback pattern from converter
  const file = fs.existsSync(file1) ? file1 : (fs.existsSync(file2) ? file2 : null);
  if (!file) return [];
  const candles = [];
  await new Promise((resolve, reject) => {
    fs.createReadStream(file).pipe(csv()).on('data', row => {
      const ts = new Date(row.timestamp);
      if (!isNaN(ts.getTime())){
        candles.push({
          ts,
          open: parseFloat(row.open),
          high: parseFloat(row.high),
          low: parseFloat(row.low),
          close: parseFloat(row.close),
          volume: parseFloat(row.volume)
        });
      }
    }).on('end', resolve).on('error', reject);
  });
  return candles;
}

async function validateOhlcvIntegrity(dateStr, symbol){
  const candles = await loadOhlcvDay(dateStr, symbol);
  const issues=[]; let ok=true;
  if (candles.length===0){ return { ok:false, issues:['NO_OHLCV'] }; }
  // Expect ~300 bars in 5-hour window (DST-aware 5 hours)
  if (candles.length<250 || candles.length>320){ issues.push(`Unexpected candle count: ${candles.length}`); }
  for (let i=1;i<candles.length;i++){
    if (!(candles[i].ts>candles[i-1].ts)){ ok=false; issues.push('Non-monotonic timestamps'); break; }
    const c=candles[i]; if(!isFinite(c.open)||!isFinite(c.high)||!isFinite(c.low)||!isFinite(c.close)){ ok=false; issues.push('NaN in OHLC'); break; }
  }
  return { ok: ok && issues.length===0, count:candles.length, issues };
}

async function computeTickHourlyDensityCSV(dateStr, symbol){
  const ticks = await loadTicksForTradingWindow(dateStr, symbol);
  const hours = getTradingHoursForDate(dateStr);
  const density={}; hours.forEach(h=>density[h]=0);
  for (const t of ticks){ density[t.t.getUTCHours()]++; }
  // basic flags
  const issues=[]; let ok=true;
  for (const h of hours){ if (density[h]===0){ ok=false; issues.push(`No ticks at UTC hour ${h}`); } }
  return { ok, density, issues };
}

function wma(series, period, idx) {
  if (idx < period - 1) return NaN;
  let ws=0, s=0; for (let j=0;j<period;j++){ const w=period-j; const v=series[idx-j]; if (!isFinite(v)) return NaN; s+=v*w; ws+=w; }
  return s/ws;
}

function rsi(closes, period, idx){ if (idx < period) return NaN; let gain=0, loss=0; for(let j=idx-period+1;j<=idx;j++){ const d=closes[j]-closes[j-1]; if (d>0) gain+=d; else loss-=d; } if (loss===0) return 100; if (gain===0) return 0; const rs=gain/period/(loss/period); return 100 - (100/(1+rs)); }

function computeIndicators(candles){
  const closes = candles.map(c=>c.close);
  return candles.map((c,i)=>({
    ...c,
    wma50: wma(closes,50,i),
    wma200: wma(closes,200,i),
    rsi: rsi(closes,14,i)
  }));
}

function threeCandleSignals(candles){
  // Simple color-only pattern: G-R-G for bullish, R-G-R for bearish
  const sigs=[];
  for(let i=200;i<candles.length;i++){
    const c3=candles[i], c2=candles[i-1], c1=candles[i-2];
    const green=c=>c.close>c.open, red=c=>c.close<c.open;
    const aboveBoth = c3.close>c3.wma50 && c3.close>c3.wma200;
    const belowBoth = c3.close<c3.wma50 && c3.close<c3.wma200;
    const rsiLongOK = c3.rsi>60; const rsiShortOK = c3.rsi<40;

    // Simple color patterns only - no body requirements
    const bull = green(c1) && red(c2) && green(c3);
    const bear = red(c1) && green(c2) && red(c3);

    if (bull && aboveBoth && rsiLongOK){ sigs.push({type:'LONG', time:c3.ts, price:c3.close}); }
    if (bear && belowBoth && rsiShortOK){ sigs.push({type:'SHORT', time:c3.ts, price:c3.close}); }
  }
  return sigs;
}

function parseTickTime(val){
  // robust parse for ts_event
  if (val==null) return null;
  const n = Number(val);
  if (!Number.isNaN(n) && Number.isFinite(n)){
    if (n > 1e15) return new Date(Math.floor(n/1e6)); // ns->ms
    if (n > 1e11) return new Date(Math.floor(n)); // ms
    return new Date(n*1000); // s
  }
  const d = new Date(val);
  return isNaN(d.getTime())?null:d;
}

async function* tickStream(dateStr, symbol, startTime, overrideFile=null){
  const defaultFile = path.join(CONFIG.tickDir, `glbx-mdp3-${dateStr}.mbp-1.${symbol}.csv`);
  const tickFile = overrideFile && fs.existsSync(overrideFile) ? overrideFile : defaultFile;
  if (!fs.existsSync(tickFile)) return;
  const hours = getTradingHoursForDate(dateStr);
  const maxH = Math.max(...hours);
  const rl = require('readline').createInterface({ input: fs.createReadStream(tickFile, { encoding: 'utf8' }) });
  let header = null; let idxTs=-1, idxBid=-1, idxAsk=-1, idxBidPx=-1, idxAskPx=-1;
  let lineCount = 0;
  for await (const line of rl){
    lineCount++;
    if (lineCount % 250000 === 0){
      // periodic heartbeat for massive files
      try { fs.appendFileSync(path.join(CONFIG.outDir,'progress','tick_read_heartbeat.log'), `${new Date().toISOString()} ${symbol} ${dateStr} line=${lineCount}\n`); } catch {}
    }
    if (!line) continue;
    if (!header){
      header = line.split(',');
      idxTs = header.indexOf('timestamp');
      if (idxTs<0) idxTs = header.indexOf('ts_event');
      idxBid = header.indexOf('bid'); idxAsk = header.indexOf('ask');
      idxBidPx = header.indexOf('bid_px_00'); idxAskPx = header.indexOf('ask_px_00');
      continue;
    }
    // guard against corrupt super-long lines: skip and continue
    if (line.length > 2000) continue;
    const cols = line.split(',');
    const tsVal = cols[idxTs]; if (tsVal==null) continue;
    const t = tsVal.includes('T') ? new Date(tsVal) : parseTickTime(tsVal);
    if (!t || !isFinite(t.getTime())) continue;
    if (t < startTime) continue;
    const h = t.getUTCHours();
    if (!hours.includes(h)) { if (h > maxH) break; else continue; }
    let bid = NaN, ask = NaN;
    if (idxBid>=0 && idxAsk>=0){ bid = Number(cols[idxBid]); ask = Number(cols[idxAsk]); }
    else if (idxBidPx>=0 && idxAskPx>=0){ bid = Number(cols[idxBidPx])/1e9; ask = Number(cols[idxAskPx])/1e9; }
    if (!isFinite(bid)||!isFinite(ask)||bid<=0||ask<=0||bid>ask) continue;
    yield { t, bid, ask };
  }
}

async function loadTicksForTradingWindow(dateStr, symbol){
  // Prefer Parquet slice for speed; fallback to CSV
  const hours = getTradingHoursForDate(dateStr);
  const hoursArg = hours.join(',');
  const parquetFile = path.join(CONFIG.parquetDir || '', `mbp1_${dateStr}_${symbol}.parquet`);
  const tmpCsv = path.join(CONFIG.outDir, 'tmp', `ticks_${dateStr}_${symbol}.csv`);
  const ticks = [];

  if (CONFIG.parquetDir && fs.existsSync(parquetFile)){
    try {
      // Slice parquet to compact CSV
      const py = process.platform === 'win32' ? 'python' : 'python3';
      const res = require('child_process').spawnSync(py, ['parquet_slice_ticks.py', '--parquet', parquetFile, '--out', tmpCsv, '--hours', hoursArg], { cwd: process.cwd(), encoding: 'utf-8' });
      // Read sliced CSV
      if (fs.existsSync(tmpCsv)){
        await new Promise((resolve, reject)=>{
          fs.createReadStream(tmpCsv).pipe(csv()).on('data', row => {
            const t = new Date(row.timestamp);
            if (!t || !isFinite(t.getTime())) return;
            const bid = Number(row.bid); const ask = Number(row.ask);
            if (!isFinite(bid)||!isFinite(ask)||bid<=0||ask<=0||bid>ask) return;
            ticks.push({ t, bid, ask });
          }).on('end', resolve).on('error', reject);
        });
      }
    } catch (e) {
      // fall back below
    }
  }

  if (ticks.length===0){
    const tickFile = path.join(CONFIG.tickDir, `glbx-mdp3-${dateStr}.mbp-1.${symbol}.csv`);
    if (!fs.existsSync(tickFile)) return [];
    await new Promise((resolve, reject) => {
      fs.createReadStream(tickFile).pipe(csv())
        .on('data', row => {
          const t = parseTickTime(row.ts_event);
          if (!t) return;
          const h = t.getUTCHours();
          if (!hours.includes(h)) return;
          const bid = Number(row.bid_px_00)/1e9; const ask = Number(row.ask_px_00)/1e9;
          if (!isFinite(bid)||!isFinite(ask)) return;
          if (bid<=0 || ask<=0) return;
          if (bid > ask) return;
          ticks.push({ t, bid, ask });
        })
        .on('end', resolve)
        .on('error', reject);
    });
  }

  ticks.sort((a,b)=>a.t-b.t);
  return ticks;
}

function findFirstTickIdx(ticks, time){
  // Binary search for efficiency
  let lo=0, hi=ticks.length-1, ans=ticks.length;
  while (lo<=hi){
    const mid=(lo+hi)>>1;
    if (ticks[mid].t >= time){ ans=mid; hi=mid-1; } else { lo=mid+1; }
  }
  return ans;
}

function validateTradeOnTicksFromArray(entry, sl, tp, ticks){
  // Synchronous on in-memory ticks array
  const startIdx = findFirstTickIdx(ticks, entry.time);
  if (startIdx >= ticks.length) return { outcome:'EXPIRED', exitTime:null, exitPrice:null };
  const firstTick = ticks[startIdx];
  const entryFillPrice = entry.type==='LONG' ? (firstTick.ask + CONFIG.slippagePointsEntry) : (firstTick.bid - CONFIG.slippagePointsEntry);
  const slLevel = entry.type==='LONG' ? (entryFillPrice - sl) : (entryFillPrice + sl);
  const tpLevel = entry.type==='LONG' ? (entryFillPrice + tp) : (entryFillPrice - tp);
  for (let i=startIdx; i<ticks.length; i++){
    const tick = ticks[i];
    if (entry.type==='LONG'){
      if (tick.ask <= slLevel){
        return { outcome:'SL', exitTime: tick.t, exitPrice: slLevel - CONFIG.slippagePointsSL };
      }
      if (tick.bid >= tpLevel){
        return { outcome:'TP', exitTime: tick.t, exitPrice: tpLevel + CONFIG.slippagePointsTP };
      }
    } else {
      if (tick.bid >= slLevel){
        return { outcome:'SL', exitTime: tick.t, exitPrice: slLevel + CONFIG.slippagePointsSL };
      }
      if (tick.ask <= tpLevel){
        return { outcome:'TP', exitTime: tick.t, exitPrice: tpLevel - CONFIG.slippagePointsTP };
      }
    }
  }
  return { outcome:'EXPIRED', exitTime:null, exitPrice:null };
}

async function validateTradeOnTicks(entry, sl, tp, dateStr, symbol, overrideFile=null){
  // Adjust entry fill to first tick after signal: long at ask, short at bid
  let filled = false; let entryFillPrice = entry.price;
  for await (const tick of tickStream(dateStr, symbol, entry.time, overrideFile)){
    // First tick becomes entry fill
    if (!filled){
      entryFillPrice = entry.type==='LONG' ? (tick.ask + CONFIG.slippagePointsEntry) : (tick.bid - CONFIG.slippagePointsEntry);
      filled = true;
    }
    const slLevel = entry.type==='LONG' ? (entryFillPrice - sl) : (entryFillPrice + sl);
    const tpLevel = entry.type==='LONG' ? (entryFillPrice + tp) : (entryFillPrice - tp);

    if (entry.type==='LONG'){
      // SL stop-market: ask <= SL level => slip
      if (tick.ask <= slLevel){
        return { outcome:'SL', exitTime: tick.t, exitPrice: slLevel - CONFIG.slippagePointsSL };
      }
      // TP limit: bid >= TP level => no slippage
      if (tick.bid >= tpLevel){
        return { outcome:'TP', exitTime: tick.t, exitPrice: tpLevel + CONFIG.slippagePointsTP };
      }
    } else {
      if (tick.bid >= slLevel){
        return { outcome:'SL', exitTime: tick.t, exitPrice: slLevel + CONFIG.slippagePointsSL };
      }
      if (tick.ask <= tpLevel){
        return { outcome:'TP', exitTime: tick.t, exitPrice: tpLevel - CONFIG.slippagePointsTP };
      }
    }
  }
  return { outcome:'EXPIRED', exitTime: null, exitPrice: null };
}

function pointsToDollars(points){ return points * CONFIG.dollarsPerPoint * CONFIG.positionSize; }

async function runCombo(sl,tp, dates){
  const comboKey = `SL${sl}_TP${tp}`;
  const tradesDir = path.join(CONFIG.outDir,'trades',comboKey); if (!fs.existsSync(tradesDir)) fs.mkdirSync(tradesDir,{recursive:true});
  let totalTrades=0, completedTrades=0, expiredTrades=0, wins=0, profit=0;
  const perDay = [];
  const durationsSec = [];
  const hourly = {}; // hour -> aggregate stats
  for (let h=0; h<24; h++){ hourly[h]={trades:0,wins:0,losses:0,profit:0,winsProfit:0,lossProfit:0,totalDurationSec:0}; }
  const auditOn = process.env.AUDIT_SIGNALS==='true';
  const auditDir = path.join(CONFIG.outDir,'signals'); if (!fs.existsSync(auditDir)) fs.mkdirSync(auditDir,{recursive:true});
  const progressFile = path.join(CONFIG.outDir,'progress','RUN_PROGRESS.json');
  const heartbeatFile = path.join(CONFIG.outDir,'progress','heartbeat.log');

  const remainingDates = dates.filter(d => !fs.existsSync(path.join(tradesDir, `${d}.json`)));
  let dayIdx = 0;
  for (const dateStr of remainingDates){
    dayIdx++;
    console.log(`[${comboKey}] Day ${dayIdx}/${remainingDates.length} -> ${dateStr}`);
    try { fs.appendFileSync(heartbeatFile, `${new Date().toISOString()} ${comboKey} ${dateStr}\n`); } catch {}

    const y = Number(dateStr.slice(0,4)), m = Number(dateStr.slice(4,6))-1, d = Number(dateStr.slice(6,8));
    const day = new Date(Date.UTC(y,m,d));
    const symbol = resolveFrontMonthSymbol(day);
    const candles = await loadOhlcvDay(dateStr, symbol);
    if (!candles || candles.length<210){ continue; }
    const withInd = computeIndicators(candles);
    // Filter signals to trading window after indicator warm-up on all-hours
    const hours = getTradingHoursForDate(dateStr);
    const signals = threeCandleSignals(withInd).filter(s => hours.includes(s.time.getUTCHours()));
    // Optional signal audit
    if (auditOn){
      const lines = ['ts,type,close,wma50,wma200,rsi'];
      for (const s of signals){ const c = withInd.find(c=>c.ts.getTime()===s.time.getTime()); if(c){ lines.push(`${s.time.toISOString()},${s.type},${c.close},${c.wma50},${c.wma200},${c.rsi}`); } }
      try { fs.writeFileSync(path.join(auditDir, `${dateStr}.csv`), lines.join('\n')); } catch {}
    }
    // Memory safety: drop ticks array between days
    if (global && global.gc) { try { global.gc(); } catch {} }

    const dayTrades = [];
    // Stream ticks once per day, process signals sequentially to avoid large arrays
    const hours2 = hours; // reuse trading window hours
    const slicedCsv = (function(){
      const parquetFile = path.join(CONFIG.parquetDir||'', `mbp1_${dateStr}_${symbol}.parquet`);
      if (CONFIG.parquetDir && fs.existsSync(parquetFile)){
        const tmpCsv = path.join(CONFIG.outDir, 'tmp', `ticks_${dateStr}_${symbol}.csv`);
        try {
          const py = process.platform==='win32' ? 'python' : 'python3';
          require('child_process').spawnSync(py, ['parquet_slice_ticks.py','--parquet', parquetFile, '--out', tmpCsv, '--hours', hours.join(',')], { cwd: process.cwd(), encoding:'utf-8' });
          if (fs.existsSync(tmpCsv)) return tmpCsv;
        } catch {}
      }
      return null;
    })();
    const overrideFile = slicedCsv && fs.existsSync(slicedCsv) ? slicedCsv : null;
    if (!overrideFile){
      console.warn(`[${comboKey}] ${dateStr} NO_SLICED_TICKS -> skip (slice-only mode)`);
      continue; // avoid raw CSV fallback to prevent huge-line crashes
    }

    if (!signals.length){
      // nothing to do
    } else {
      let sigIdx = 0;
      let inTrade = false;
      let entryFillPrice = null;
      let entryFillTime = null;
      let entrySig = null;
      let slLevel = null, tpLevel = null;
      const startTime = signals[0].time;

      for await (const tick of tickStream(dateStr, symbol, startTime, overrideFile)){
        if (!inTrade){
          // Try to open next signal only when we are at/after its time
          if (sigIdx < signals.length && signals[sigIdx].time <= tick.t){
            entrySig = signals[sigIdx];
            entryFillPrice = entrySig.type==='LONG' ? (tick.ask + CONFIG.slippagePointsEntry) : (tick.bid - CONFIG.slippagePointsEntry);
            entryFillTime = tick.t;
            slLevel = entrySig.type==='LONG' ? (entryFillPrice - sl) : (entryFillPrice + sl);
            tpLevel = entrySig.type==='LONG' ? (entryFillPrice + tp) : (entryFillPrice - tp);
            inTrade = true;
          }
        } else {
          // Manage open trade
          if (entrySig.type==='LONG'){
            if (tick.ask <= slLevel){
              const exitTime = tick.t;
              const exitPrice = slLevel - CONFIG.slippagePointsSL;
              const pnlPoints = -sl;
              const pnl = pointsToDollars(pnlPoints);
              const durationSec = Math.max(0, Math.round((exitTime - entryFillTime)/1000));
              durationsSec.push(durationSec);
              const entryHour = entrySig.time.getUTCHours();
              hourly[entryHour].trades += 1; hourly[entryHour].losses += 1; hourly[entryHour].profit += pnl; hourly[entryHour].lossProfit += pnl; hourly[entryHour].totalDurationSec += durationSec;
              dayTrades.push({ date: dateStr, symbol, type: entrySig.type, entryHourUTC: entryHour, entryTime: entrySig.time.toISOString(), entryPrice: entrySig.price, wma50_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma50 ?? null, wma200_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma200 ?? null, rsi_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.rsi ?? null, sl, tp, outcome: 'SL', exitTime: exitTime.toISOString(), exitPrice, durationSec, pnl });
              totalTrades++; inTrade=false; if (pnlPoints>0) wins++; profit += pnl;
              // Skip any signals that occurred while trade was open
              while (++sigIdx < signals.length && signals[sigIdx].time <= exitTime) {}
              entrySig=null; entryFillTime=null; entryFillPrice=null;
              continue;
            }
            if (tick.bid >= tpLevel){
              const exitTime = tick.t;
              const exitPrice = tpLevel + CONFIG.slippagePointsTP;
              const pnlPoints = tp;
              const pnl = pointsToDollars(pnlPoints);
              const durationSec = Math.max(0, Math.round((exitTime - entryFillTime)/1000));
              durationsSec.push(durationSec);
              const entryHour = entrySig.time.getUTCHours();
              hourly[entryHour].trades += 1; hourly[entryHour].wins += 1; hourly[entryHour].profit += pnl; hourly[entryHour].winsProfit += pnl; hourly[entryHour].totalDurationSec += durationSec;
              dayTrades.push({ date: dateStr, symbol, type: entrySig.type, entryHourUTC: entryHour, entryTime: entrySig.time.toISOString(), entryPrice: entrySig.price, wma50_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma50 ?? null, wma200_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma200 ?? null, rsi_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.rsi ?? null, sl, tp, outcome: 'TP', exitTime: exitTime.toISOString(), exitPrice, durationSec, pnl });
              totalTrades++; wins++; profit += pnl; inTrade=false;
              while (++sigIdx < signals.length && signals[sigIdx].time <= exitTime) {}
              entrySig=null; entryFillTime=null; entryFillPrice=null;
              continue;
            }
          } else {
            if (tick.bid >= slLevel){
              const exitTime = tick.t;
              const exitPrice = slLevel + CONFIG.slippagePointsSL;
              const pnlPoints = -sl;
              const pnl = pointsToDollars(pnlPoints);
              const durationSec = Math.max(0, Math.round((exitTime - entryFillTime)/1000));
              durationsSec.push(durationSec);
              const entryHour = entrySig.time.getUTCHours();
              hourly[entryHour].trades += 1; hourly[entryHour].losses += 1; hourly[entryHour].profit += pnl; hourly[entryHour].lossProfit += pnl; hourly[entryHour].totalDurationSec += durationSec;
              dayTrades.push({ date: dateStr, symbol, type: entrySig.type, entryHourUTC: entryHour, entryTime: entrySig.time.toISOString(), entryPrice: entrySig.price, wma50_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma50 ?? null, wma200_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma200 ?? null, rsi_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.rsi ?? null, sl, tp, outcome: 'SL', exitTime: exitTime.toISOString(), exitPrice, durationSec, pnl });
              totalTrades++; inTrade=false; if (pnlPoints>0) wins++; profit += pnl;
              while (++sigIdx < signals.length && signals[sigIdx].time <= exitTime) {}
              entrySig=null; entryFillTime=null; entryFillPrice=null;
              continue;
            }
            if (tick.ask <= tpLevel){
              const exitTime = tick.t;
              const exitPrice = tpLevel - CONFIG.slippagePointsTP;
              const pnlPoints = tp;
              const pnl = pointsToDollars(pnlPoints);
              const durationSec = Math.max(0, Math.round((exitTime - entryFillTime)/1000));
              durationsSec.push(durationSec);
              const entryHour = entrySig.time.getUTCHours();
              hourly[entryHour].trades += 1; hourly[entryHour].wins += 1; hourly[entryHour].profit += pnl; hourly[entryHour].winsProfit += pnl; hourly[entryHour].totalDurationSec += durationSec;
              dayTrades.push({ date: dateStr, symbol, type: entrySig.type, entryHourUTC: entryHour, entryTime: entrySig.time.toISOString(), entryPrice: entrySig.price, wma50_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma50 ?? null, wma200_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.wma200 ?? null, rsi_at_entry: withInd.find(c=>c.ts.getTime()===entrySig.time.getTime())?.rsi ?? null, sl, tp, outcome: 'TP', exitTime: exitTime.toISOString(), exitPrice, durationSec, pnl });
              totalTrades++; wins++; profit += pnl; inTrade=false;
              while (++sigIdx < signals.length && signals[sigIdx].time <= exitTime) {}
              entrySig=null; entryFillTime=null; entryFillPrice=null;
              continue;
            }
          }
        }
      }
      // If stream ends with open trade, mark expired
      if (inTrade && entrySig){
        dayTrades.push({ date: dateStr, symbol, type: entrySig.type, entryHourUTC: entrySig.time.getUTCHours(), entryTime: entrySig.time.toISOString(), entryPrice: entrySig.price, sl, tp, outcome: 'EXPIRED', exitTime: null, exitPrice: null, durationSec: null, pnl: 0 });
        totalTrades++;
      }
    }
    fs.writeFileSync(path.join(tradesDir, `${dateStr}.json`), JSON.stringify(dayTrades,null,2));
    perDay.push({ date: dateStr, trades: dayTrades.length, profit: dayTrades.reduce((s,t)=>s+t.pnl,0) });
  }
  const wr = totalTrades>0 ? (wins/totalTrades*100) : 0;
  const avgDurationSec = durationsSec.length? durationsSec.reduce((a,b)=>a+b,0)/durationsSec.length : 0;
  const medianDurationSec = durationsSec.length? (arr=>{ const s=[...arr].sort((a,b)=>a-b); const mid=Math.floor(s.length/2); return s.length%2?s[mid]:(s[mid-1]+s[mid])/2; })(durationsSec) : 0;
  function pct(arr,p){ if(!arr.length) return 0; const s=[...arr].sort((a,b)=>a-b); const i=Math.floor((p/100)*(s.length-1)); return s[i]; }
  const p75=pct(durationsSec,75), p90=pct(durationsSec,90), p95=pct(durationsSec,95);
  const hourlyBreakdown = Object.keys(hourly).map(h=>({ hourUTC:Number(h), trades:hourly[h].trades, wins:hourly[h].wins, losses:hourly[h].losses, profit:hourly[h].profit, winsProfit:hourly[h].winsProfit, lossProfit:hourly[h].lossProfit, avgDurationSec: hourly[h].trades? (hourly[h].totalDurationSec/hourly[h].trades):0 }));
  // Equity curve and drawdowns
  let equity=0, peak=0, maxDD=0, sumWins=0, sumLosses=0;
  for (const d of perDay){ equity += d.profit; if (equity>peak) peak=equity; const dd = peak - equity; if (dd>maxDD) maxDD=dd; if (d.profit>0) sumWins+=d.profit; else sumLosses+=d.profit; }
  const profitFactor = sumLosses!==0 ? (sumWins/Math.abs(sumLosses)) : (sumWins>0 ? Infinity : 0);
  const expectancy = totalTrades>0 ? (profit/totalTrades) : 0;
  const summary = { sl, tp, totalTrades, wins, winRate: wr, profit, avgDurationSec, medianDurationSec, p75, p90, p95, profitFactor, expectancy, maxDrawdown:maxDD, hourlyBreakdown };
  // Persist a minimal summary AND the per-day table separately for readability
  fs.writeFileSync(path.join(CONFIG.outDir,'summaries',`${comboKey}.json`), JSON.stringify(summary,null,2));
  fs.writeFileSync(path.join(CONFIG.outDir,'summaries',`${comboKey}.per_day.json`), JSON.stringify(perDay,null,2));
  return { ...summary, perDay };
}

async function main(){
  ensureDirs();
  console.log('🚀 Tick-validated front-month grid test starting...');
  console.log(`Output -> ${CONFIG.outDir}`);
  let dates = listUniqueDatesFromOHLCV();
  const sampleMonth = process.env.SAMPLE_MONTH; // e.g., '2024-09'
  const startDate = process.env.START_DATE; // e.g., '2024-09-01'
  const endDate = process.env.END_DATE;     // e.g., '2024-09-30'
  const contract = process.env.CONTRACT;    // e.g., 'MNQU4' or 'MNQZ4'

  if (contract){
    // Keep only days where the resolved front-month matches CONTRACT
    dates = dates.filter(ds => resolveFrontMonthSymbol(new Date(Date.UTC(Number(ds.slice(0,4)), Number(ds.slice(4,6))-1, Number(ds.slice(6,8))))) === contract);
  }
  if (sampleMonth){
    const ym = sampleMonth.replace(/-/g,'');
    dates = dates.filter(d => d.startsWith(ym));
  }
  if (startDate || endDate){
    const start = startDate ? startDate.replace(/-/g,'') : '00000000';
    const end = endDate ? endDate.replace(/-/g,'') : '99999999';
    dates = dates.filter(d => d>=start && d<=end);
  }
  console.log(`📅 Days to run: ${dates.length}`);

  const combos=[]; for(const sl of CONFIG.slRange){ for(const tp of CONFIG.tpRange){ combos.push({sl,tp}); }}
  console.log(`📊 Total combos: ${combos.length}`);

  const allSummaries=[]; let idx=0;
  const progressFile = path.join(CONFIG.outDir,'progress','RUN_PROGRESS.json');
  const doneCombos = new Set();
  if (fs.existsSync(progressFile)){
    try { const prev=JSON.parse(fs.readFileSync(progressFile,'utf8')); (prev.done||[]).forEach(k=>doneCombos.add(k)); } catch {}
  }
  for (const {sl,tp} of combos){
    const comboKey=`SL${sl}_TP${tp}`;
    if (doneCombos.has(comboKey)) { console.log(`\n⏭️  Skipping completed combo ${comboKey}`); continue; }
    idx++;
    console.log(`\n⚡ Running combo ${idx}/${combos.length}: SL=${sl}, TP=${tp}`);
    const s = await runCombo(sl,tp,dates);
    console.log(`   -> Trades=${s.totalTrades}, WR=${s.winRate.toFixed(1)}%, PnL=$${s.profit.toFixed(2)}, AvgDur=${(s.avgDurationSec||0).toFixed(1)}s, MedianDur=${(s.medianDurationSec||0)}s`);
    allSummaries.push(s);
    // Persist running table and progress
    fs.writeFileSync(path.join(CONFIG.outDir,'summaries','ALL_SUMMARIES.json'), JSON.stringify(allSummaries,null,2));
    try {
      const current = fs.existsSync(progressFile) ? JSON.parse(fs.readFileSync(progressFile,'utf8')) : { done: [] };
      current.done = Array.from(new Set([...(current.done||[]), comboKey]));
      current.last = { sl, tp, finishedAt: new Date().toISOString() };
      fs.writeFileSync(progressFile, JSON.stringify(current,null,2));
    } catch {}
  }

  // Deliverables
  const byProfit = [...allSummaries].sort((a,b)=>b.profit-a.profit);
  const byWR = [...allSummaries].sort((a,b)=>b.winRate-a.winRate);
  const topN = CONFIG.topN;
  const deliverable = { generatedAt: new Date().toISOString(), days: dates.length, combos: combos.length, topN, topByProfit: byProfit.slice(0,topN), topByWinRate: byWR.slice(0,topN), all: allSummaries }; // all includes avg/median duration and hourly breakdown per combo
  const base = `summary_${new Date().toISOString().replace(/[:.]/g,'-')}`;
  fs.writeFileSync(path.join(CONFIG.outDir, `${base}.json`), JSON.stringify(deliverable,null,2));
  const toCsv = (rows)=>{ if(!rows.length) return ''; const headers = Object.keys(rows[0]); return [headers.join(',')].concat(rows.map(r=>headers.map(h=>r[h]).join(','))).join('\n'); };
  fs.writeFileSync(path.join(CONFIG.outDir, `${base}_top_by_profit.csv`), toCsv(deliverable.topByProfit));
  fs.writeFileSync(path.join(CONFIG.outDir, `${base}_top_by_winrate.csv`), toCsv(deliverable.topByWinRate));
  fs.writeFileSync(path.join(CONFIG.outDir, `${base}_all_summaries.csv`), toCsv(allSummaries));
  console.log('\n🏁 COMPLETE. Expanded deliverables written.');
}

main().catch(err=>{ console.error('Fatal error:', err); process.exit(1); });

