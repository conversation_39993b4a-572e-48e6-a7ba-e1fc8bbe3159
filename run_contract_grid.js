const { spawnSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const contract = process.argv[2];
const patternMode = (process.argv[3] || process.env.PATTERN_MODE || 'SIMPLE').toUpperCase();
const slArg = process.argv[4] || process.env.SL_RANGE || '1-8';
const tpArg = process.argv[5] || process.env.TP_RANGE || '3,4,5,6,7,8,9,10,12,14,16,18,20,22,24';
const startArg = process.argv[6] || process.env.START_DATE || '';
const endArg = process.argv[7] || process.env.END_DATE || '';
if (!contract) {
  console.error('Usage: node run_contract_grid.js <CONTRACT> [PATTERN_MODE] [SL_RANGE] [TP_LIST] [START_DATE] [END_DATE]');
  console.error('Example: node run_contract_grid.js MNQU4 SIMPLE 2-8 3,4,5,6,7,8,9,10,12,14,16,18,20,22,24 2024-08-15 2024-08-21');
  process.exit(1);
}

const env = {
  ...process.env,
  CONTRACT: contract,
  PATTERN_MODE: patternMode,
  OHLCV_DIR: process.env.OHLCV_DIR || 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/ohlcv_all_hours',
  OUT_DIR: process.env.OUT_DIR || `D:/backtest-output/tick-grid-front-month-${contract}`,
  SL_RANGE: slArg,
  TP_RANGE: tpArg,
  START_DATE: startArg,
  END_DATE: endArg,
};

console.log('Starting contract grid with:', {
  CONTRACT: env.CONTRACT,
  PATTERN_MODE: env.PATTERN_MODE,
  OHLCV_DIR: env.OHLCV_DIR,
  OUT_DIR: env.OUT_DIR,
  SL_RANGE: env.SL_RANGE,
  TP_RANGE: env.TP_RANGE,
  START_DATE: env.START_DATE,
  END_DATE: env.END_DATE,
});

// Do NOT clear OUT_DIR: we want to resume and skip completed work
const res = spawnSync(process.platform === 'win32' ? 'node.exe' : 'node', ['tick_grid_front_month.js'], {
  cwd: __dirname,
  env,
  stdio: 'inherit',
});
process.exit(res.status || 0);

