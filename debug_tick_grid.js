const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Debug script to identify why tick grid tests are showing 0 trades
const CONFIG = {
  ohlcvDir: 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/ohlcv',
  tickDir: 'D:/backtest-output/MNQ_MBP1_Aug2024_Aug2025/decompressed',
  tradingHoursUTCStandard: [13, 14, 15, 16, 17], // 7–11am CST
  tradingHoursUTCDST: [12, 13, 14, 15, 16],      // 7–11am CDT
};

// Test a single day to see what's happening
async function debugSingleDay() {
  console.log('🔍 DEBUGGING TICK GRID ISSUES...\n');
  
  // Check if directories exist
  console.log('📁 Checking directories:');
  console.log(`OHLCV Dir exists: ${fs.existsSync(CONFIG.ohlcvDir)}`);
  console.log(`Tick Dir exists: ${fs.existsSync(CONFIG.tickDir)}`);
  
  if (!fs.existsSync(CONFIG.ohlcvDir)) {
    console.log('❌ OHLCV directory not found!');
    return;
  }
  
  // List available OHLCV files
  const ohlcvFiles = fs.readdirSync(CONFIG.ohlcvDir).filter(f => f.endsWith('.csv'));
  console.log(`\n📊 Found ${ohlcvFiles.length} OHLCV files`);
  console.log('First 5 files:', ohlcvFiles.slice(0, 5));
  
  // Test with a recent file
  const testFile = ohlcvFiles.find(f => f.includes('20250101') || f.includes('20241201') || f.includes('MNQH5'));
  if (!testFile) {
    console.log('❌ No suitable test file found');
    return;
  }
  
  console.log(`\n🧪 Testing with file: ${testFile}`);
  
  // Load and analyze the OHLCV data
  const filePath = path.join(CONFIG.ohlcvDir, testFile);
  const candles = [];
  
  return new Promise((resolve) => {
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        const ts = new Date(row.timestamp);
        if (!isNaN(ts.getTime())) {
          candles.push({
            ts,
            open: parseFloat(row.open),
            high: parseFloat(row.high),
            low: parseFloat(row.low),
            close: parseFloat(row.close),
            volume: parseInt(row.volume) || 0
          });
        }
      })
      .on('end', () => {
        console.log(`📈 Loaded ${candles.length} candles`);
        
        if (candles.length === 0) {
          console.log('❌ No candles loaded - check CSV format');
          resolve();
          return;
        }
        
        // Sort by timestamp
        candles.sort((a, b) => a.ts.getTime() - b.ts.getTime());
        
        console.log(`📅 Date range: ${candles[0].ts.toISOString()} to ${candles[candles.length-1].ts.toISOString()}`);
        
        // Add indicators
        console.log('\n🔢 Adding indicators...');
        const withIndicators = addIndicators(candles);
        
        // Check for valid indicators
        const validIndicators = withIndicators.filter(c => c.wma50 && c.wma200 && c.rsi);
        console.log(`✅ ${validIndicators.length} candles with valid indicators`);
        
        if (validIndicators.length === 0) {
          console.log('❌ No valid indicators - check calculation');
          resolve();
          return;
        }
        
        // Test signal generation
        console.log('\n🎯 Testing signal generation...');
        const signals = threeCandleSignals(withIndicators);
        console.log(`📊 Generated ${signals.length} signals`);
        
        if (signals.length > 0) {
          console.log('First 3 signals:');
          signals.slice(0, 3).forEach((sig, i) => {
            console.log(`  ${i+1}. ${sig.type} at ${sig.time.toISOString()} price=${sig.price}`);
          });
        }
        
        // Test time filtering
        console.log('\n⏰ Testing time filtering...');
        const tradingHours = CONFIG.tradingHoursUTCStandard; // Assume standard time for now
        const filteredSignals = signals.filter(sig => {
          const hour = sig.time.getUTCHours();
          return tradingHours.includes(hour);
        });
        
        console.log(`🕐 ${filteredSignals.length} signals in trading hours (${tradingHours.join(', ')} UTC)`);
        
        if (filteredSignals.length > 0) {
          console.log('Trading hour signals:');
          filteredSignals.slice(0, 5).forEach((sig, i) => {
            console.log(`  ${i+1}. ${sig.type} at ${sig.time.toISOString()} (${sig.time.getUTCHours()}:00 UTC) price=${sig.price}`);
          });
        }
        
        resolve();
      })
      .on('error', (err) => {
        console.log('❌ Error reading CSV:', err.message);
        resolve();
      });
  });
}

// Simple WMA calculation
function wma(values, period) {
  if (values.length < period) return null;
  const weights = Array.from({length: period}, (_, i) => i + 1);
  const weightSum = weights.reduce((a, b) => a + b, 0);
  const weightedSum = values.slice(-period).reduce((sum, val, i) => sum + val * weights[i], 0);
  return weightedSum / weightSum;
}

// Simple RSI calculation
function rsi(prices, period = 14) {
  if (prices.length < period + 1) return null;
  
  const changes = [];
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i-1]);
  }
  
  if (changes.length < period) return null;
  
  const recentChanges = changes.slice(-period);
  const gains = recentChanges.filter(c => c > 0);
  const losses = recentChanges.filter(c => c < 0).map(c => Math.abs(c));
  
  const avgGain = gains.length > 0 ? gains.reduce((a, b) => a + b, 0) / period : 0;
  const avgLoss = losses.length > 0 ? losses.reduce((a, b) => a + b, 0) / period : 0;
  
  if (avgLoss === 0) return 100;
  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
}

// Add indicators to candles
function addIndicators(candles) {
  const result = [];
  
  for (let i = 0; i < candles.length; i++) {
    const candle = {...candles[i]};
    
    // Get price arrays up to current point
    const closes = candles.slice(0, i + 1).map(c => c.close);
    
    // Calculate WMAs
    candle.wma50 = wma(closes, 50);
    candle.wma200 = wma(closes, 200);
    
    // Calculate RSI
    candle.rsi = rsi(closes, 14);
    
    // Add RSI-MA (8-period SMA of RSI)
    if (i >= 7) {
      const rsiValues = result.slice(-7).map(c => c.rsi).filter(r => r !== null);
      if (rsiValues.length === 7 && candle.rsi !== null) {
        rsiValues.push(candle.rsi);
        candle.rsiMA = rsiValues.reduce((a, b) => a + b, 0) / 8;
      }
    }
    
    result.push(candle);
  }
  
  return result;
}

// Three candle signal detection
function threeCandleSignals(candles) {
  const mode = 'STRICT'; // Use strict mode
  const bodyHigh = c => Math.max(c.open, c.close);
  const bodyLow = c => Math.min(c.open, c.close);
  const signals = [];
  
  for (let i = 200; i < candles.length; i++) {
    const c3 = candles[i], c2 = candles[i-1], c1 = candles[i-2];
    
    // Skip if missing indicators
    if (!c3.wma50 || !c3.wma200 || !c3.rsi || !c3.rsiMA) continue;
    
    const green = c => c.close > c.open;
    const red = c => c.close < c.open;
    
    const aboveBoth = c3.close > c3.wma50 && c3.close > c3.wma200;
    const belowBoth = c3.close < c3.wma50 && c3.close < c3.wma200;
    
    const rsiLongOK = c3.rsi > c3.rsiMA && c3.rsi > 60;
    const rsiShortOK = c3.rsi < c3.rsiMA && c3.rsi < 40;
    
    let bull = false, bear = false;
    
    if (mode === 'STRICT') {
      bull = green(c1) && red(c2) && green(c3) && (c3.close > bodyHigh(c2));
      bear = red(c1) && green(c2) && red(c3) && (c3.close < bodyLow(c2));
    }
    
    if (bull && aboveBoth && rsiLongOK) {
      signals.push({type: 'LONG', time: c3.ts, price: c3.close});
    }
    if (bear && belowBoth && rsiShortOK) {
      signals.push({type: 'SHORT', time: c3.ts, price: c3.close});
    }
  }
  
  return signals;
}

debugSingleDay().catch(console.error);
